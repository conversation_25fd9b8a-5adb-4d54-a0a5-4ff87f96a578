# kaggle3.py Environment Bootstrap

This directory contains a Riemannian Causal GNN implementation for link prediction with hyperbolic geometry.

## Quick Setup

### Option 1: pip (recommended)
```bash
pip install -r requirements.txt
```

### Option 2: conda
```bash
conda env create -f environment.yml
conda activate kaggle3
```

### Option 3: Check your current environment
```bash
python setup_environment.py
```

## System Requirements

- **Python**: ≥3.8
- **RAM**: 16GB+ recommended 
- **GPU**: Optional but recommended (RTX 2060 Mobile detected)
- **CUDA**: Requires NVIDIA driver installation for GPU acceleration

## Key Dependencies

- **PyTorch** with CUDA support
- **PyTorch Geometric** for graph neural networks
- **Geoopt** for hyperbolic/Riemannian optimization
- **NetworkX** for path analysis 
- **scikit-learn** for metrics

## Additional Notes

1. **GPU Setup**: NVIDIA drivers need to be installed/updated for CUDA support
2. **Memory**: The code includes mixed precision training for efficient GPU memory usage
3. **CPU Optimization**: Intel Extension for PyTorch (IPEX) included for Intel CPUs
4. **Data Formats**: pyarrow/fastparquet included for efficient data I/O

## Files Generated

- `requirements.txt` - pip dependencies
- `environment.yml` - conda environment specification  
- `system_specs.md` - detailed system specifications for performance baselines
- `setup_environment.py` - dependency checker and setup validator

## Running kaggle3.py

After setting up the environment:

```bash
python kaggle3.py
```

The script includes:
- Dummy data generation for testing
- GPU/CPU fallback handling
- PyTorch compilation and optimization
- Profiling capabilities
- Model checkpointing

## Performance Notes

Current system (Intel i7-9750H, RTX 2060 Mobile, 16GB RAM):
- Batch sizes should be conservative (≤4096) due to GPU memory constraints
- CPU fallback available with 12-thread optimization
- Mixed precision training enabled for memory efficiency
