from torch_geometric.loader import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LinkNeighbor<PERSON>oader, LinkLoader
from torch_geometric.sampler import NeighborSampler
from torch_geometric.sampler import NegativeSampling
import torch, pyg_lib
import torch.nn.functional as F
from torch import nn
from torch_geometric.data import Data
from torch_geometric.nn import MessagePassing
from torch_geometric.utils import k_hop_subgraph, negative_sampling
from torch_geometric.utils import index_to_mask
from torchmetrics import AUROC, AveragePrecision
from torch_geometric.datasets import OGB_MAG
from torch_geometric.transforms import ToUndirected, ToDevice
from torch_geometric.data import HeteroData
import torch.serialization as _ser
import torch.profiler as prof, time, itertools
import torch._dynamo
from itertools import islice
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
import geoopt
from geoopt.manifolds import PoincareBall
import networkx as nx
import nx_parallel as nxp
import matplotlib.pyplot as plt
from collections import defaultdict
import json 
import os
import gc
import uuid
from functools import wraps
import inspect
import types, sys
import torch_geometric.utils as pyg_utils
#device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

def setup_ddp(rank, world_size):
    os.environ["MASTER_ADDR"] = "127.0.0.1"
    os.environ["MASTER_PORT"] = "29500"      # free port
    torch.cuda.set_device(rank)
    torch.distributed.init_process_group("nccl",
                                         rank=rank,
                                         world_size=world_size)

def cleanup_ddp():
    torch.distributed.destroy_process_group()


def main_worker(rank: int, world_size: int):
    setup_ddp(rank, world_size)
    device = torch.device(f"cuda:{rank}" if torch.cuda.is_available() else "cpu")

  
    if (
        "weights_only" in inspect.signature(torch.load).parameters
        and not hasattr(torch, "_full_pickle_ok")
    ):
        _orig_load = torch.load                     # save pristine ref
    
        # build a wrapper *with the exact same signature* dynamically
        from functools import wraps, update_wrapper
        from inspect import signature, Parameter
    
        sig = signature(_orig_load)
        params = list(sig.parameters.values())
    
        # create a new function with identical parameters
        def _wrapper(*args, **kwargs):
            kwargs.setdefault("weights_only", False)
            return _orig_load(*args, **kwargs)
    
        # re-apply the original signature so keyword `f=` still works
        _wrapper = update_wrapper(_wrapper, _orig_load)
        _wrapper.__signature__ = sig               # for Python <3.10
    
        torch.load = _wrapper
        torch._full_pickle_ok = True               # sentinel flag
    
    
    gc.collect()
    
    bundle = torch.load("/kaggle/working/preprocessed_data.pt")
    data = bundle["data"]
    train_edges = bundle["train_edges"]
    valid_edges = bundle["valid_edges"]
    test_edges = bundle["test_edges"]
    nodes_df = bundle["nodes_df"]
    node_type = bundle["node_type"]
    n_nodes = data.num_nodes
    torch._dynamo.config.suppress_errors = True
    torch._dynamo.config.dynamic_shapes = True
    torch._dynamo.config.cache_size_limit = 512
    torch._inductor.config.triton.cudagraph_dynamic_shape_warn_limit=None
    
    os.environ["NX_CUGRAPH_AUTOCONFIG"] = "True"
    os.environ["NETWORKX_AUTOMATIC_BACKENDS"] = "parallel"
    os.environ["TORCH_LOGS"]            = "+dynamo"
    os.environ["TORCHDYNAMO_VERBOSE"]   = "1"
    torch.set_num_threads(os.cpu_count())
    #torch.set_num_interop_threads(1)
    
    
    #torch.set_num_threads(os.cpu_count())
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.ipc_collect()
        os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True,max_split_size_mb:64"
        torch.cuda.empty_cache()
        for d in range(torch.cuda.device_count()):
            torch.cuda.set_per_process_memory_fraction(0.80, device=d)
        scaler = torch.amp.GradScaler("cuda")
    else:
        scaler = torch.amp.GradScaler("cpu")
        
    def assert_batch_consistent(batch: Data):
        """Sanity‑check that no index in the mini‑batch is ≥ num_nodes."""
        N = batch.x.size(0)         # number of *local* nodes in this mini‑graph
    
        emax = batch.edge_index.max().item()
        emin = batch.edge_index.min().item()
    
        if emax >= N or emin < 0:
            mask = (batch.edge_index[0] >= N) | (batch.edge_index[1] >= N) | \
                   (batch.edge_index <   0).any(dim=0)
            bad  = batch.edge_index[:, mask.nonzero().flatten()[:20]]  # first 20
            raise RuntimeError(
                f"\n[ASSERT] edge_index out of range: "
                f"min={emin}, max={emax}, num_nodes={N}\n"
                f"offending edges (first 20):\n{bad.t()}\n"
            )
    
    def json2vec(s): 
        return np.array(json.loads(s), dtype=np.float32)
    
    print("Loading data...")
    #nodes_df = pd.read_csv("/kaggle/input/primekg/nodes.csv", engine = 'pyarrow', dtype={"node_index": np.int64, "node_type":  "category"})
    #n_nodes = nodes_df.shape[0]
    
    #node_types = nodes_df['node_type'].tolist()
    #nodes_df["type_id"]=nodes_df["node_type"].cat.codes
    #node_type = torch.tensor(nodes_df["type_id"].to_numpy(), dtype=torch.long)
    #num_nodes = torch.tensor(nodes_df["node_index"].to_numpy(), dtype=torch.long)
    
    #edges_df = pd.read_csv("/kaggle/input/primekg/edges.csv", engine = 'pyarrow')
    #edge_index = torch.tensor(edges_df[['x_index', 'y_index']].values.T, dtype=torch.long) #, device=device
    
    #features_df = pd.read_csv("/kaggle/input/primekg/feature-embd2.csv", low_memory=False)
    #features_df = features_df.sort_values('node_index')
    #emb_arr = np.vstack(features_df['embedding'].map(json2vec).values)
    #x = torch.tensor(emb_arr, dtype=torch.float) #, device=device
    
    #data = Data(x=x, edge_index=edge_index) #.to(device)
    #data.num_nodes = len(nodes_df["node_index"])
    #global_edge_index  = data.edge_index.to(device)
    #neg_edge_index = negative_sampling(
    #    edge_index=edge_index,
    #    num_nodes=n_nodes,
    #    num_neg_samples=edge_index.size(1),
    #)
    #neg_df = pd.DataFrame(
    #    {
    #        "x_index": neg_edge_index[0].cpu().numpy(),
    #        "y_index": neg_edge_index[1].cpu().numpy(),
    #        "label":   np.zeros(neg_edge_index.size(1), dtype=np.int8),
    #    }
    #)
    
    #pos_df = edges_df[['x_index', 'y_index']].copy()
    #pos_df['label'] = 1
    #link_df = pd.concat([pos_df, neg_df], ignore_index=True)
    #train_edges, test_edges = train_test_split(edges_df[['x_index', 'y_index']], test_size=0.2, random_state=42)
    #train_edges = train_edges.reset_index(drop=True)
    #test_edges = test_edges.reset_index(drop=True)
    #del edges_df
    #del features_df
    #gc.collect()
    
    #print("done1")
    ENABLE_EXPLANATION = (rank == 0)

    class CausalAttention(nn.Module):
        """
        Flexible causal head that works on any B × N × D tensor.
    
        Arguments
        ---------
        hidden_dim : int
        n_heads     : int  (multi-head improves stability but keep ≤8 on 24 GB GPUs)
        dropout     : float
        """
        def __init__(self, hidden_dim: int, n_heads: int = 4, dropout: float = 0.1):
            super().__init__()
            self.h = n_heads
            self.d = hidden_dim // n_heads
    
            # one linear per projection, bias=False to keep math identical to your W_ij
            self.q_proj = nn.Linear(hidden_dim, hidden_dim, bias=False)
            self.k_proj = nn.Linear(hidden_dim, hidden_dim, bias=False)
            self.v_proj = nn.Linear(hidden_dim, hidden_dim, bias=False)
            self.out    = nn.Linear(hidden_dim, hidden_dim, bias=False)
            self.drop   = nn.Dropout(dropout)
            self.scale  = self.d ** -0.5             # √d  from Transformer
    
        def forward(self, x, adj_mask):
            """
            x         : (B, N, D)
            adj_mask  : (B, N, N)  bool or 0/-inf tensor
                        True / 1   if j can causally affect i
            Returns
            -------
            out       : (B, N, D)
            alpha     : (B, N, N)  causal attention weights (detach if you just visualise)
            """
            B, N, _ = x.shape
    
            # linear projections → (B, h, N, d)
            q = self.q_proj(x).view(B, N, self.h, self.d).transpose(1, 2)
            k = self.k_proj(x).view(B, N, self.h, self.d).transpose(1, 2)
            v = self.v_proj(x).view(B, N, self.h, self.d).transpose(1, 2)
    
            attn_logits = (q @ k.transpose(-2, -1)) * self.scale     # (B, h, N, N)
    
            # mask out disallowed edges
            mask = adj_mask.unsqueeze(1)         # broadcast over h
            attn_logits = attn_logits.masked_fill(~mask, float("-inf"))
    
            alpha = torch.softmax(attn_logits, dim=-1)               # (B, h, N, N)
            alpha = self.drop(alpha)
    
            # causal message passing
            z = alpha @ v                                            # (B, h, N, d)
            z = z.transpose(1, 2).contiguous().view(B, N, -1)        # concat heads
            out = F.relu(self.out(z))
    
            return out, alpha.detach()
            
            
    def build_adj_mask(edge_index, num_nodes, device):
        src, dst = edge_index            # (E,)
        # --- flat index trick ---------------------------------------------
        idx = dst * num_nodes + src      # (E,)  linear indices
        mask = torch.zeros(num_nodes * num_nodes,
                           dtype=torch.bool, device=device)
        mask.index_fill_(0, idx, True)   # ✓ supported op
        # --- clear diagonal without fill_diagonal_ ------------------------
        diag_idx = torch.arange(num_nodes, device=device)
        mask.index_fill_(0, diag_idx * (num_nodes + 1), False)
        return mask.view(1, num_nodes, num_nodes)
    
    class HyperbolicGCN(MessagePassing):
        def __init__(self, in_dim, out_dim, manifold=None, c=1.0):
            super(HyperbolicGCN, self).__init__(aggr='add')
            self.linear = nn.Linear(in_dim, out_dim, bias=False)
            self.manifold = manifold or PoincareBall(c=c)
    
        def forward(self, x, edge_index):
            h = self.manifold.expmap0(x)
            return self.propagate(edge_index, x=h)
    
        def message(self, x_j):
            tangent = self.manifold.logmap0(x_j)
            transformed = self.linear(tangent)
            h = self.manifold.expmap0(transformed)
            return h
    
        def update(self, aggr_out):
            return aggr_out
    
    class RiemannCausalGNN(nn.Module):
        def __init__(
            self,
            in_channels: int,
            hidden_channels: int,
            num_vars: int,
            c: float = 1.0,
            link_mode: str = "hyperbolic",   #  "hyperbolic" | "mlp"
            dist_scale: float = 1.0,
        ):
            super().__init__()
            self.encoder = nn.Linear(in_channels, hidden_channels)
            self.manifold = PoincareBall(c=c)
            self.hgnn1 = HyperbolicGCN(hidden_channels, hidden_channels, self.manifold)
            self.hgnn2 = HyperbolicGCN(hidden_channels, hidden_channels, self.manifold)
            self.causal_head = CausalAttention(hidden_channels)
            self.classifier = nn.Linear(hidden_channels, 1)
            self.link_mode    = link_mode
            self.dist_scale   = dist_scale
        
        # -----------------------------------------------------------
        # 1)  FAST,  differentiable link-prediction branch
        # -----------------------------------------------------------
        def _score_edges(self, x_local, edge_index_local, edge_pairs):
            # 1) GNN encoder
            h = F.relu(self.encoder(x_local))
            h = self.hgnn1(h, edge_index_local)
            h = self.hgnn2(h, edge_index_local)           # (N, H)
            N = h.size(0)
            if N <= 1_024:                               # safe threshold for 16 GB GPUs
                
                # 2) build causal mask  (dense Bool, shape 1×N×N)
                adj_mask = build_adj_mask(edge_index_local, N, h.device)
                if h.is_cuda:
                    with torch.amp.autocast('cuda', dtype=torch.float16):
                        # 3) attention in the *tangent* space
                        z_tan          = self.manifold.logmap0(h)
                        causal_out, a  = self.causal_head(z_tan.unsqueeze(0), adj_mask)
                else:                                             # CPU → no AMP
                    z_tan          = self.manifold.logmap0(h)
                    causal_out, a  = self.causal_head(z_tan.unsqueeze(0), adj_mask)            
                
                #alpha_mean     = a.mean(dim=1).squeeze(0)     # (N, N)  — keep if you log it
            
                # 4) fuse attention output (skip-connection style)
                h = h + causal_out.squeeze(0)
            
                # ------- optional: symmetric normalised Laplacian -------------
                if getattr(self, "use_laplacian", False):
                    A      = adj_mask[0].float()
                    deg    = A.sum(-1).clamp(min=1)
                    D_inv  = deg.pow(-0.5)
                    L_sym  = D_inv.view(-1,1) * A * D_inv.view(1,-1)
                    h      = L_sym @ h                        # smooth features
        
            # 5) link score
            src, dst = edge_pairs
            if self.link_mode == "hyperbolic":
                dist   = self.manifold.dist(h[src], h[dst])
                scores = -self.dist_scale * dist          # logits
            else:
                scores = self.classifier(h[src] * h[dst]).squeeze(-1)
            return scores
    
        # -----------------------------------------------------------
        # 2)  HEAVY explanation branch (never traced / compiled)
        # -----------------------------------------------------------
        @torch._dynamo.disable               # <- keeps Dynamo out
        def explain_paths(
            self,
            x_local,
            edge_index_local,
            edge_pairs,
            n_id,
            filter_types=None,
            aggregate_importance=True,
            export_file=None,
        ):
            """
            Almost verbatim copy of your original '# --- path extraction …'
            loop, moved out of `forward` so the fast path stays clean.
    
            Returns
            -------
            paths : list[list[str]]
                Human-readable path strings for each (drug, disease) pair.
            node_scores : dict or None
            """
            paths        = []
            node_scores  = defaultdict(float) if aggregate_importance else None
            # drug --> source , disease --> target
            for drug, disease in edge_pairs.t().tolist():
                # ---- build 4-hop sub-graph around the pair -----------------
                seeds = torch.tensor([drug, disease],
                                     device=edge_index_local.device)
                sub_nodes, sub_edge_index, mapping, _ = k_hop_subgraph(
                    seeds, num_hops=4, edge_index=edge_index_local,
                    relabel_nodes=True,
                )
    
                sub_x = x_local[sub_nodes].to(self.encoder.weight.device)
                x     = F.relu(self.encoder(sub_x))
                x     = self.hgnn1(x, sub_edge_index)
                x     = self.hgnn2(x, sub_edge_index)
    
                # ---- causal attention & edge weights -----------------------
                z_tan   = self.manifold.logmap0(x)
                z_batch = z_tan.unsqueeze(0)
                adj_mask = build_adj_mask(sub_edge_index, z_tan.size(0), x.device)
                causal_out, alpha = self.causal_head(z_batch, adj_mask)
                alpha_mean = alpha.mean(dim=1).squeeze(0).float().cpu().numpy()
                
                x = x + causal_out.squeeze(0)
            
                # ------- optional: symmetric normalised Laplacian -------------
                if getattr(self, "use_laplacian", False):
                    A      = adj_mask[0].float()
                    deg    = A.sum(-1).clamp(min=1)
                    D_inv  = deg.pow(-0.5)
                    L_sym  = D_inv.view(-1,1) * A * D_inv.view(1,-1)
                    x      = L_sym @ x                        # smooth features
            
                # 5) link score
                drug_idx, disease_idx = mapping.tolist()
                if self.link_mode == "hyperbolic":
                    dist   = self.manifold.dist(x[drug_idx], x[disease_idx])
                    scores = -self.dist_scale * dist          # logits
                else:
                    scores = self.classifier(x[drug_idx] * x[disease_idx]).squeeze(-1)
                      
                
                contrib_weights = scores[disease_idx]            # j → disease
    
                # ---- top-N nodes by attention contribution ---------------
                top_nodes = [
                    i for i in np.argsort(contrib_weights)[::-1]
                    if i != disease_idx
                ][:5]
    
                # ---- enumerate simple paths (≤5 hops) ---------------------
                g = nx.DiGraph()
                g.add_nodes_from(sub_nodes.cpu().tolist())
                g.add_edges_from([
                    (int(sub_nodes[u]), int(sub_nodes[v]))
                    for u, v in sub_edge_index.t().cpu().numpy()])
    
                scored_paths = []
                try:
                    for path in nx.all_simple_paths(
                        g,
                        source=int(sub_nodes[drug_idx]),
                        target=int(sub_nodes[disease_idx]),
                        cutoff=5,
                    ):
                        w = 1.0
                        for u, v in zip(path[:-1], path[1:]):
                            try:
                                i = (sub_nodes == v).nonzero(as_tuple=True)[0]
                                j = (sub_nodes == u).nonzero(as_tuple=True)[0]
                                w *= float(alpha_mean[i, j])
                            except IndexError:
                                w = 0.0
                                break
                        scored_paths.append((w, path))
                except nx.NetworkXNoPath:
                    pass
    
                scored_paths.sort(key=lambda t: t[0], reverse=True)
    
                readable_paths = []
                for w, path in scored_paths[:3]:
                    readable = []
                    valid_path = True
                    for node_id in path:
                        try:     
                            row = nodes_df.loc[nodes_df.node_index
                                               == int(n_id[node_id])].iloc[0]
                        except (KeyError, IndexError):
                            row = {"node_type": "paper", "node_name": f"paper_{int(n_id[node_id])}"}
                            
                        ntype = row["node_type"]
                        if filter_types and ntype not in filter_types:
                            valid_path = False
                            break
                        idx        = (sub_nodes == node_id).nonzero(as_tuple=True)[0][0]
                        node_score = float(contrib_weights[idx])
                        readable.append(
                            f"{ntype}:{row['node_name']}({node_score:.3f})"
                        )
                        if aggregate_importance:
                            node_scores[node_id] += 1
                    if valid_path:
                        readable_paths.append(f"[{w:.4f}] " + " → ".join(readable))
    
                paths.append(readable_paths)
            now=str(uuid.uuid1())
            export_file=f"/kaggle/working/primekg/primekg_explanations_{now}.json"
            if export_file and paths:     # optional dump to disk
                with open(export_file, "w") as f:
                    for i, pset in enumerate(paths):
                        f.write(f"Pair {i} paths:\n")
                        for p in pset:
                            f.write(f"  {p}\n")
                        f.write("\n")
    
            return paths, (dict(node_scores) if aggregate_importance else None)
                        
        # -----------------------------------------------------------
        # 3)  Unified public API
        # -----------------------------------------------------------
        def forward(
            self,
            x_local,
            edge_index_local,
            edge_pairs,
            n_id,
            explain: bool = False,
            **kw,                       # forwards filter_types/aggregate_importance/…
        ):
            """
            *Always* uses `_score_edges` for logits.
            Heavy explanation code runs only if `explain=True`.
            """
            logits = self._score_edges(x_local, edge_index_local, edge_pairs)
    
            if not explain:
                return logits.to(x_local.device), None, None
    
            paths, node_scores = self.explain_paths(
                x_local, edge_index_local, edge_pairs, n_id, **kw)
            return logits.to(x_local.device), paths, node_scores
    
    def edge_batch_to_tensor(t):
        #return torch.tensor(df[['x_index', 'y_index']].to_numpy().T, dtype=torch.long)
        if isinstance(t, torch.Tensor):
            return t.long() if t.shape[0] == 2 else t.t().contiguous().long()
        raise TypeError("edge index must be a Tensor")
    
    @torch.no_grad()
    def validate(
        model,
        loader,
        device,
        criterion,
        explain=False,
        explain_sample=False,   # sample just one edge for a demo explanation
        k_list=(1, 3, 10, 20),
    ):
        model.eval()
        auroc = AUROC(task="binary").to(device)
        ap    = AveragePrecision(task="binary").to(device)
        hits_correct = {k: 0 for k in k_list}
        hits_total   = 0
        total_loss = 0.0
        correct = 0
        total   = 0
        have_sampled = False
    
        with torch.inference_mode():
            for batch in loader:
                batch = batch.to(device, non_blocking=True)
    
                # ---------- fast path (no explanations) -------------------
                # forward_args(batch) must return:
                #   (batch.x, batch.edge_index, batch.edge_label_index, batch.n_id)
                with torch.amp.autocast(device_type="cuda", dtype=torch.float16): #--> FOR CUDA    
                    y_pred, _, _ = model(*forward_args(batch), explain=False)
    
                    y_true = batch.edge_label.float()
                    total_loss += criterion(y_pred, y_true).item() * y_true.numel()
    
                prob = torch.sigmoid(y_pred)
                auroc.update(prob,  y_true)
                pred = prob > 0.5
                correct += (pred == y_true.bool()).sum().item()
                total   += y_true.size(0)
                #hits_total += y_true.size(0)
                for k in k_list:
                    topk = prob.topk(k).indices               # global top-k
                    hits_correct[k] += y_true[topk].sum().item()          
    
    
            
                # ---------- heavy path on ONE random pair ----------------
                if explain and explain_sample and not have_sampled:
                    k = torch.randint(0, batch.edge_label_index.size(1), ())
                    one_pair = batch.edge_label_index[:, k : k + 1]   # (2, 1)
    
                    _, paths, node_scores = model(
                        batch.x,
                        batch.edge_index,       # message-passing graph
                        one_pair,               # labelled edge to explain
                        batch.n_id,
                        explain=True
                    )
    
                    # log_explanation(paths, node_scores, one_pair, epoch=global_epoch)
                    have_sampled = True
            
            metrics = {
                "AUROC" : auroc.compute().item(),
                "AP"    : ap.compute().item(),
            }
            for k in k_list:
                metrics[f"Hits@{k}"] = hits_correct[k] / max(total, 1)
            return metrics   
        
        acc = correct / total
        return total_loss / len(loader.dataset), acc
    
    print("done2")
    
    
    model = RiemannCausalGNN(in_channels=data.x.size(1), hidden_channels=64, num_vars=n_nodes).to(device)
    # optional: Intel IPEX optimisation (works only on CPU builds of PyTorch)
    #try:
    #    import intel_extension_for_pytorch as ipex
    #    model = ipex.optimize(model, inplace=True)
    #except ImportError:
    #    pass
    
    data.to(device)
    def forward_args(batch):
        """
        Returns the positional argument tuple expected by model(...).
        Adapt this if your model signature changes.
        """
        return (
            batch.x,                 # node features               (N, F)
            batch.edge_index,        # COO edges used by the GNN   (2, E)
            batch.edge_label_index,  # (2, B)   positive+negative edges for loss
            batch.n_id,              # global → local node mapping
        )
    
    
    print("Starting samplers...")
    train_edge_index = edge_batch_to_tensor(train_edges)
    test_edge_index = edge_batch_to_tensor(valid_edges)
    edge_sampler = NeighborSampler(
        data,
        num_neighbors=[5, 3, 3],      # ← fan-out per hop
        subgraph_type='induced',   # default; OK for link-pred
        replace=False,
        disjoint=False,                 # keeps each (u,v) edge isolated
    )
    
    shard = slice(rank, train_edge_index.size(1), world_size)
    local_train_edge_index = train_edge_index[:, shard].contiguous()
    
    
    train_loader = LinkLoader(
            data,
            link_sampler=edge_sampler,       # ⬅ same object
            edge_label_index=local_train_edge_index,
            batch_size=1_024,
            neg_sampling=NegativeSampling('binary', amount=1.0),
            shuffle=False,
            num_workers=4,
            persistent_workers=True,
    )
    
    
    sharde = slice(rank, test_edge_index.size(1), world_size)
    local_test_edge_index = test_edge_index[:, sharde].contiguous()
    
    eval_loader = LinkLoader(
            data,
            link_sampler=edge_sampler,       # ⬅ reuse the cache
            edge_label_index=local_test_edge_index, # your validation positives
            batch_size=2_048,                # often you can go larger
            neg_sampling=NegativeSampling('binary', amount=1.0),
            shuffle=False,                   # keep order stable
            num_workers=4,
            persistent_workers=True,
    )
    print("done Samplers")
    
    
    print("we start here compile")
    
    orig = model._score_edges
    def _score_edges_fp16(*a, **kw):
        with torch.amp.autocast('cuda', dtype=torch.float16):
            return orig(*a, **kw)
    
    torch._inductor.config.triton.cudagraphs = False
    torch._inductor.config.max_autotune = False
    torch._inductor.config.max_autotune_gemm = False
    
    #model._score_edges = torch.compile(_score_edges_fp16, mode="reduce-overhead", dynamic=True, fullgraph=False)
    model._score_edges = _score_edges_fp16
    model = torch.nn.parallel.DistributedDataParallel(model,
                                      device_ids=[rank],
                                      output_device=rank,
                                      find_unused_parameters=True)
    print("torch compile ends")
    warm_batch = next(iter(LinkLoader(
            data,
            link_sampler=edge_sampler,       # ⬅ same object
            edge_label_index=train_edge_index,
            batch_size=128,
            neg_sampling=NegativeSampling('binary', amount=1.0),
            shuffle=False,
            num_workers=0,
            persistent_workers=False, 
    )))
    warm_batch2 = next(iter(LinkLoader(
            data,
            link_sampler=edge_sampler,       # ⬅ reuse the cache
            edge_label_index=test_edge_index, # your validation positives
            batch_size=256,                # often you can go larger
            neg_sampling=NegativeSampling('binary', amount=1.0),
            shuffle=False,                   # keep order stable
            num_workers=0,
            persistent_workers=False,
    )))
    # variant A – the one used every training step
    
    print("train model compiling")
    model.train()
    with torch.no_grad():
        loss_fn = torch.nn.BCEWithLogitsLoss()
        logits, *_ = model(*forward_args(warm_batch), explain=False)
        #(loss_fn(logits, torch.zeros_like(logits))).backward()
    del warm_batch
    
    model.eval()
    with torch.no_grad():                             # no need for grad here
        # variant B – causal explanation path (runs only occasionally)
        print("eval model compiling")
        _ = model(*forward_args(warm_batch2), explain=False)
        #print("edge check model")
        #_ = model(*forward_args(warm_batch2), explain=True)
    del warm_batch2
    gc.collect()
    torch.cuda.empty_cache()
    
    optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
    criterion = nn.BCEWithLogitsLoss()
    
    scheduler = torch.optim.lr_scheduler.CyclicLR(optimizer, base_lr=0.0001, max_lr=0.1,step_size_up=2_000,mode="triangular2", cycle_momentum=False)
    #scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.7, patience=5, threshold=0.00001, threshold_mode='rel', cooldown=0, min_lr=0, eps=1e-08)
    
    
    data.node_type = node_type
    num_cpus = max(4, os.cpu_count() // 2)
    print("done compiling")
    #scores, paths, node_imp = model(*forward_args(warm_batch),
    #                                explain=True,
    #                                filter_types={"gene", "disease"})
    #print(scores, paths, node_imp)
    
    
    
    
    print("Starting training...")
    best_loss= 1000000.0
    
    for epoch in range(1, 21):
        model.train()
        if hasattr(train_loader.sampler, "set_epoch"):
        	train_loader.sampler.set_epoch(epoch)
        for batch in train_loader:
            #assert_batch_consistent(batch) #--> DEBUG
            batch = batch.to(device, non_blocking=True)
            edge_pairs_local = batch.edge_label_index          # 2 × B
            y_true = batch.edge_label.float().to(device)
            optimizer.zero_grad(set_to_none=True)
            
            with torch.amp.autocast(device_type="cuda", dtype=torch.float16): #--> FOR CUDA
                y_pred, *_ = model(*forward_args(batch), explain=False) 
            #model(batch.x, batch.edge_index, edge_pairs_local, batch.n_id, explain=False)      # pass mapping too
            
                loss = criterion(y_pred, y_true)
            #loss.backward(retain_graph=True) #--> DEBUGloss.backward()
            
            #torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            #optimizer.step()
            #scheduler.step()
            
            #print("encoder.grad:", model.encoder.weight.grad.norm()) #--> DEBUG
            #model.zero_grad() #--> DEBUG
            
            scaler.scale(loss).backward() #--> FOR CUDA
            scaler.unscale_(optimizer)  #--> FOR CUDA
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)  #--> FOR CUDA
            scaler.step(optimizer)  #--> FOR CUDA       
            scaler.update()  #--> FOR CUDA
            scheduler.step()  #--> FOR CUDA
    
        
        # ------------------------- evaluation ----------------------------
        test_loss, acc = validate(model, eval_loader, device, criterion, explain=ENABLE_EXPLANATION, explain_sample=True) 
            
        if test_loss < best_loss:
            print("saving model")
            if rank == 0:
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'scheduler_state_dict': scheduler.state_dict(),
                    'loss': loss,
                    'test_loss': test_loss,}, f"/kaggle/working/primekg/best_state_{test_loss}.pt")
                best_loss=test_loss
                
        print(f"Epoch {epoch:03d} | Train Loss: {loss:.4f} | Test Loss: {test_loss:.4f} | Acc: {acc:.4f}")
    

    
    
    
    print("start 1 batch profiling")
    def run_one_batch():
        batch = next(iter(train_loader))          # ← fixed
        edge_pairs_local = batch.edge_label_index
        batch = batch.to("cpu", non_blocking=True)
        y_hat, *_ = model(batch.x, batch.edge_index,edge_pairs_local,
                          batch.n_id, explain=False)
        (y_hat * 0).sum().backward()              # dummy loss
    
    with prof.profile(with_flops=True, record_shapes=True) as p:
        run_one_batch()
    print(p.key_averages().table(sort_by="self_cpu_time_total", row_limit=10))
    
    if rank == 0:
        print("Done Training")
        cleanup_ddp()

def safe_main_worker(rank, world_size):
    try:
        main_worker(rank, world_size)
    finally:
        try:
            if torch.distributed.is_initialized():
                torch.distributed.destroy_process_group()
        except Exception as e:
            print(f"[Rank {rank}] DDP cleanup error: {e}")

if __name__ == "__main__":
    if (
        "weights_only" in inspect.signature(torch.load).parameters
        and not hasattr(torch, "_full_pickle_ok")
    ):
        _orig_load = torch.load                     # save pristine ref

        # build a wrapper *with the exact same signature* dynamically
        from functools import wraps, update_wrapper
        from inspect import signature, Parameter

        sig = signature(_orig_load)
        params = list(sig.parameters.values())

        # create a new function with identical parameters
        def _wrapper(*args, **kwargs):
            kwargs.setdefault("weights_only", False)
            return _orig_load(*args, **kwargs)

        # re-apply the original signature so keyword `f=` still works
        _wrapper = update_wrapper(_wrapper, _orig_load)
        _wrapper.__signature__ = sig               # for Python <3.10

        torch.load = _wrapper
        torch._full_pickle_ok = True               # sentinel flag


    print("Loading torch-geometric dataset  OGB_MAG (preprocess=metapath2vec) …")
    dataset = OGB_MAG(root="kaggle/working/data",
                  preprocess="metapath2vec",       # adds 128-d embeddings to *all* node types
                  transform=ToUndirected(merge=True), force_reload = False)

    hetero_data = dataset[0]                          # HeteroData object

    # ➊  keep only the paper–paper citation edges for LP:
    paper_edge_index = hetero_data["paper", "cites", "paper"].edge_index
    paper_nodes = hetero_data["paper"].x
    paper_node_type = torch.full((paper_nodes.size(0),), 3, dtype=torch.long)

    # ➋  convert whole hetero graph to homogeneous so “node_type” is retained
    #data = hetero_data.to_homogeneous(node_attrs=["x"],edge_attrs=[],add_node_type=True)
    data = Data(
        x=paper_nodes,
        edge_index=paper_edge_index,
        node_type=paper_node_type,
        node_index=torch.arange(paper_nodes.size(0)),
        n_id=torch.arange(paper_nodes.size(0)),
    )
    # ➌  replace edge_index with paper–paper sub-graph (undirected, contiguous)
    #data.edge_index = paper_edge_index
    #if data.edge_type:
    #    del data.edge_type                     # not needed after sub-graphing
    use_subgraph = True
    if use_subgraph:
        anchor_nodes = torch.randperm(data.num_nodes)[:50_000]
        subset, edge_index, mapping, edge_mask = pyg_utils.k_hop_subgraph(
            anchor_nodes, num_hops=2, edge_index=data.edge_index, relabel_nodes=True
        )

        data = Data(
            x=data.x[subset],
            edge_index=edge_index,
            node_type=data.node_type[subset],
            node_index=torch.arange(subset.size(0)),
            n_id=torch.arange(subset.size(0)),
        )
        n_nodes = data.num_nodes

    else:   
        # ➍  explicit node ids (expected downstream)
        n_nodes             = data.num_nodes
        data.node_index     = torch.arange(n_nodes, dtype=torch.long)
        data.n_id           = data.node_index        # kept for loaders

    # ➎  float32 features (AMP/Dynamo prefer uniform dtypes)
    data.x = data.x.float()

    print(f"done1  (N={n_nodes:,},  E={data.edge_index.size(1):,})")

    # simple 90/5/5 random split for link prediction
    perm       = torch.randperm(data.edge_index.size(1))
    num_train  = int(0.90 * perm.size(0))
    num_valid  = int(0.05 * perm.size(0))
    train_edges = data.edge_index[:, perm[:num_train]]
    valid_edges = data.edge_index[:, perm[num_train:num_train + num_valid]]
    test_edges  = data.edge_index[:, perm[num_train + num_valid:]]

    # explanations are safe now (synthetic names/types below)
    if use_subgraph:
         nodes_df = pd.DataFrame({
             "node_index": data.node_index.numpy(),
             "node_type" : data.node_type.numpy(),
         })
    else:
        nodes_df = pd.DataFrame({
        "node_index": torch.arange(n_nodes).numpy(),
        # use original type-ids from HeteroToHomogeneous (0=author,1=inst,2=field,3=paper)
        "node_type" : data.node_type.numpy(),
        })
    nodes_df["node_name"] = [
    f"type{t}_{i}" for t, i in zip(nodes_df.node_type, nodes_df.node_index)
    ]
    nodes_df["node_type"] = nodes_df["node_type"].astype("category")
    nodes_df["type_id"]   = nodes_df["node_type"].cat.codes
    node_type=nodes_df["node_type"]

    # Save all artifacts
    torch.save({
        "data": data,
        "train_edges": train_edges,
        "valid_edges": valid_edges,
        "test_edges": test_edges,
        "nodes_df": nodes_df,
        "node_type": nodes_df["node_type"],
    }, "/kaggle/working/preprocessed_data.pt")
    
    
    
    os.environ["MASTER_ADDR"] = "127.0.0.1"
    os.environ["MASTER_PORT"] = "29500"
    world_size = torch.cuda.device_count()          # 2 on Kaggle
    torch.multiprocessing.set_start_method("spawn", force=True)
    torch.multiprocessing.spawn(
        safe_main_worker,                # <- this symbol must be _top-level_
        args=(world_size,),
        nprocs=world_size,
        join=True,
    )

