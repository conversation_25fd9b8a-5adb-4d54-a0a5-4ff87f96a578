# High-Level Architecture - kaggle3.py

## Table of Contents
- [Overview](#overview)
- [Major Component Blocks](#major-component-blocks)
- [Module Dependency Graph](#module-dependency-graph)
- [External Dependencies](#external-dependencies)
- [Tensor Dimension Trace](#tensor-dimension-trace)
- [Link-Prediction & Causal Attention Logic Analysis](#link-prediction--causal-attention-logic-analysis)
- [Message Flow Sequence Diagrams](#message-flow-sequence-diagrams)
- [Architecture Characteristics](#architecture-characteristics)

---

*Related Documentation:*
- [Model & Training Enhancements →](brainstorm.md)
- [Bug Fix Reasoning →](bugfix_reason.md)

---

## Overview

The `kaggle3.py` file implements a hyperbolic graph neural network (GNN) for link prediction with causal attention mechanisms. The system is designed for drug-disease prediction tasks using Riemannian geometry on the Poincaré ball manifold. The architecture combines geometric deep learning with causal inference and includes both fast inference paths and detailed explanation capabilities.

## Major Component Blocks

### 1. Data Loading & Preprocessing (Lines 82-136)
- **Dummy Data Generation**: Creates synthetic node and edge data for testing
- **Node Features**: Random 16-dimensional embeddings for 100 nodes
- **Edge Structure**: 300 random edges with train/test split (80/20)
- **PyG Data Object**: Wraps data in PyTorch Geometric format

### 2. Sampler Creation (Lines 564-597)
- **NeighborSampler**: Multi-hop neighborhood sampling with fan-out [5, 3, 3]
- **LinkLoader**: Batch processing for link prediction tasks
- **Negative Sampling**: Binary negative sampling with 1:1 ratio
- **Train/Eval Loaders**: Separate loaders with different batch sizes (4096/8192)

### 3. Model Architecture (Lines 142-454)

#### Core Components:
- **CausalAttention** (Lines 142-196): Multi-head attention with causal masking
- **HyperbolicGCN** (Lines 211-228): Message passing on Poincaré ball manifold
- **RiemannCausalGNN** (Lines 230-454): Main model combining hyperbolic GNN with causal attention

#### Model Flow:
```
Input Features → Encoder → HyperbolicGCN → HyperbolicGCN → CausalAttention → Link Scoring
```

### 4. Training Loop (Lines 646-693)
- **Mixed Precision Training**: Uses CUDA autocast and GradScaler
- **Gradient Clipping**: Max norm of 1.0 for stability
- **Cyclic Learning Rate**: Triangular scheduling
- **Model Checkpointing**: Saves best validation loss model
- **Validation**: Comprehensive metrics including AUROC, AP, and Hits@K

### 5. Profiling & Optimization (Lines 698-710)
- **PyTorch Profiler**: Performance analysis with FLOPS counting
- **Torch Compile**: JIT compilation for `_score_edges` method
- **Memory Management**: CUDA cache management and garbage collection

## Module Dependency Graph

```
Data Loading
    ↓
Samplers (NeighborSampler, LinkLoader)
    ↓
RiemannCausalGNN
    ├── CausalAttention
    ├── HyperbolicGCN (x2 layers)
    └── Manifold Operations (PoincareBall)
    ↓
Training Loop
    ├── Loss Computation
    ├── Backpropagation
    └── Validation
    ↓
Profiling & Analysis
```

## External Dependencies

The following external libraries are referenced and assumed to exist on the target server:

### Core ML/DL Frameworks:
- **PyTorch**: Core tensor operations, autograd, mixed precision
- **PyTorch Geometric (PyG)**: Graph neural network components
  - `torch_geometric.loader`: NeighborLoader, LinkNeighborLoader, LinkLoader
  - `torch_geometric.sampler`: NeighborSampler, NegativeSampling
  - `torch_geometric.nn`: MessagePassing base class
  - `torch_geometric.utils`: Graph utilities (k_hop_subgraph, negative_sampling)

### Geometric Deep Learning:
- **Geoopt**: Riemannian optimization and manifold operations
  - `geoopt.manifolds.PoincareBall`: Hyperbolic geometry

### Scientific Computing:
- **NumPy**: Numerical arrays and operations
- **Pandas**: Data manipulation and analysis
- **Scikit-learn**: Machine learning utilities
  - Model selection (train_test_split)
  - Metrics (roc_auc_score, average_precision_score)

### Graph Analysis:
- **NetworkX**: Graph algorithms and path finding
- **Matplotlib**: Visualization (imported but not actively used)

### System & Optimization:
- **Intel Extension for PyTorch (IPEX)**: CPU optimization (optional)
- **TorchMetrics**: Evaluation metrics (referenced in task but not directly imported)

### Utilities:
- **Collections**: defaultdict for aggregation
- **JSON**: Data serialization
- **UUID**: Unique identifier generation
- **OS/GC**: System resource management

## Tensor Dimension Trace

### Symbolic Notation
- `B` = batch size (varies: 4096 train, 8192 eval)
- `N` = number of nodes in subgraph (varies per batch)
- `E` = number of edges in subgraph 
- `F` = input feature dimension (16 for dummy data, 384 for real features)
- `H` = hidden dimension (64)
- `h` = number of attention heads (4)
- `d` = per-head dimension = H/h = 16
- `P` = number of edge pairs for prediction

### Forward Pass Tensor Evolution

| Operation | Input Shape | Output Shape | Location | Notes |
|-----------|-------------|--------------|----------|-------|
| **Data Loading** | | | | |
| `x` (node features) | - | `(N, F)` | Line 106 | F=16 dummy, F=384 real |
| `edge_index` (COO format) | - | `(2, E)` | Line 102 | E≈300 dummy, E≈4M real |
| **Batch Loading** | | | | |
| `batch.x` | `(N, F)` | `(N, F)` | LinkLoader | Subgraph node features |
| `batch.edge_index` | `(2, E)` | `(2, E)` | LinkLoader | Subgraph edges |
| `batch.edge_label_index` | - | `(2, P)` | LinkLoader | Edge pairs to predict |
| `batch.edge_label` | - | `(P,)` | LinkLoader | Binary labels |
| **RiemannCausalGNN._score_edges** | | | | |
| **1. Encoder** | | | | |
| `x_local` → `h` | `(N, F)` | `(N, H)` | Line 255 | Linear projection |
| **2. HyperbolicGCN Layer 1** | | | | |
| `h` → `h_euclidean` | `(N, H)` | `(N, H)` | Line 218 | expmap0: tangent→manifold |
| Message passing | `(N, H)` | `(N, H)` | Line 219 | propagate() |
| Per message: `x_j` | `(E, H)` | `(E, H)` | Line 221 | Neighbor features |
| Per message: `tangent` | `(E, H)` | `(E, H)` | Line 222 | logmap0: manifold→tangent |
| Per message: `transformed` | `(E, H)` | `(E, H)` | Line 223 | Linear transformation |
| Per message: `h_msg` | `(E, H)` | `(E, H)` | Line 224 | expmap0: tangent→manifold |
| Aggregated output | `(N, H)` | `(N, H)` | Line 227 | Sum aggregation |
| **3. HyperbolicGCN Layer 2** | | | | |
| Same as Layer 1 | `(N, H)` | `(N, H)` | Line 257 | Identical operations |
| **4. Causal Attention (if N ≤ 4096)** | | | | |
| `z_tan` | `(N, H)` | `(N, H)` | Line 265 | logmap0: manifold→tangent |
| Add batch dim | `(N, H)` | `(1, N, H)` | Line 266 | unsqueeze(0) |
| **CausalAttention.forward** | | | | |
| Input `x` | `(B, N, H)` | - | Line 175 | B=1 in this case |
| **Query projection** | | | | |
| `q_proj(x)` | `(B, N, H)` | `(B, N, H)` | Line 178 | Linear transform |
| Reshape | `(B, N, H)` | `(B, N, h, d)` | Line 178 | view() |
| Transpose | `(B, N, h, d)` | `(B, h, N, d)` | Line 178 | transpose(1,2) |
| **Key projection** | | | | |
| `k_proj(x)` | `(B, N, H)` | `(B, N, H)` | Line 179 | Linear transform |
| Reshape | `(B, N, H)` | `(B, N, h, d)` | Line 179 | view() |
| Transpose | `(B, N, h, d)` | `(B, h, N, d)` | Line 179 | transpose(1,2) |
| **Value projection** | | | | |
| `v_proj(x)` | `(B, N, H)` | `(B, N, H)` | Line 180 | Linear transform |
| Reshape | `(B, N, H)` | `(B, N, h, d)` | Line 180 | view() |
| Transpose | `(B, N, h, d)` | `(B, h, N, d)` | Line 180 | transpose(1,2) |
| **Attention computation** | | | | |
| `q @ k.T` | `(B, h, N, d)`, `(B, h, d, N)` | `(B, h, N, N)` | Line 182 | Scaled dot-product |
| `attn_logits` | `(B, h, N, N)` | `(B, h, N, N)` | Line 182 | After scaling |
| **Causal masking** | | | | |
| `adj_mask` | `(1, N, N)` | `(1, 1, N, N)` | Line 185 | unsqueeze(1) |
| `masked_fill` | `(B, h, N, N)` | `(B, h, N, N)` | Line 186 | Apply mask |
| **Attention weights** | | | | |
| `alpha` | `(B, h, N, N)` | `(B, h, N, N)` | Line 188 | softmax |
| **Value aggregation** | | | | |
| `alpha @ v` | `(B, h, N, N)`, `(B, h, N, d)` | `(B, h, N, d)` | Line 192 | Attention-weighted values |
| Transpose | `(B, h, N, d)` | `(B, N, h, d)` | Line 193 | transpose(1,2) |
| Reshape | `(B, N, h, d)` | `(B, N, H)` | Line 193 | view() to concat heads |
| `out(z)` | `(B, N, H)` | `(B, N, H)` | Line 194 | Output projection |
| **Back to RiemannCausalGNN** | | | | |
| Remove batch dim | `(1, N, H)` | `(N, H)` | Line 271 | squeeze(0) |
| Skip connection | `(N, H)`, `(N, H)` | `(N, H)` | Line 271 | h + causal_out |
| **5. Link Scoring** | | | | |
| `edge_pairs` | `(2, P)` | - | Line 282 | src, dst indices |
| **Hyperbolic scoring** | | | | |
| `h[src]` | `(P, H)` | `(P, H)` | Line 284 | Source node embeddings |
| `h[dst]` | `(P, H)` | `(P, H)` | Line 284 | Target node embeddings |
| `manifold.dist()` | `(P, H)`, `(P, H)` | `(P,)` | Line 284 | Hyperbolic distances |
| `scores` | `(P,)` | `(P,)` | Line 285 | Final logits |
| **Loss Computation** | | | | |
| `y_pred` | `(P,)` | `(P,)` | Line 656 | Model output |
| `y_true` | `(P,)` | `(P,)` | Line 652 | Ground truth |
| `loss` | - | `()` | Line 659 | BCEWithLogitsLoss |

### Auxiliary Operations

| Operation | Shape | Location | Purpose |
|-----------|-------|----------|----------|
| `build_adj_mask` | `(1, N, N)` | Line 209 | Causal attention mask |
| `k_hop_subgraph` | Various | Line 321 | Explanation subgraphs |
| `negative_sampling` | `(2, E_neg)` | LinkLoader | Generate negative edges |

### Memory Complexity Analysis

- **Attention mechanism**: O(N²) for adjacency mask and attention weights
- **Hyperbolic operations**: O(N×H) for manifold computations  
- **Message passing**: O(E×H) for edge-based operations
- **Critical bottleneck**: Attention computation when N > 4096 (bypassed)

## Link-Prediction & Causal Attention Logic Analysis

### Edge Production, Batching & Consumption

#### Positive Edge Generation
- **Source**: Training edges from `train_edges` DataFrame (80% of original data)
- **Format**: `(2, P)` tensor via `edge_batch_to_tensor()` → `torch.tensor(df[['x_index', 'y_index']].T)`
- **Batching**: LinkLoader samples subgraphs around positive edges using NeighborSampler
- **Consumer**: `batch.edge_label_index` contains both positive and negative edge pairs

#### Negative Edge Generation  
- **Source**: `NegativeSampling('binary', amount=1.0)` in LinkLoader
- **Strategy**: Binary negative sampling with 1:1 ratio (equal positive/negative samples)
- **Method**: Random sampling of non-existent edges from the graph
- **Integration**: Combined with positive edges in `batch.edge_label_index`

#### Edge Consumption Pipeline
```
LinkLoader Batch Creation:
1. Sample k-hop subgraph around positive edges
2. Generate equal number of negative edges  
3. Combine: batch.edge_label_index = [pos_edges, neg_edges]  # (2, 2P)
4. Create labels: batch.edge_label = [1s, 0s]              # (2P,)
5. Forward to model._score_edges()
```

### Link Scoring Branches: Hyperbolic vs MLP

#### Hyperbolic Distance Branch (`link_mode="hyperbolic"`) 
**Location**: Lines 283-285
```python
if self.link_mode == "hyperbolic":
    dist = self.manifold.dist(h[src], h[dst])     # Poincaré distance
    scores = -self.dist_scale * dist              # Convert to logits
```

**Mathematics**:
- **Poincaré Distance**: `d(u,v) = arcosh(1 + 2||u-v||²/((1-||u||²)(1-||v||²)))`
- **Curvature**: Controlled by `c=1.0` in PoincareBall manifold
- **Scaling**: `dist_scale` parameter adjusts logit magnitude
- **Interpretation**: Closer nodes → smaller distance → higher score (more likely link)

#### MLP Branch (`link_mode="mlp"`)
**Location**: Lines 286-287
```python
else:
    scores = self.classifier(h[src] * h[dst]).squeeze(-1)  # Element-wise product + MLP
```

**Architecture**:
- **Input**: Hadamard product `h[src] ⊙ h[dst]` of node embeddings
- **Network**: Single linear layer `nn.Linear(hidden_channels, 1)`
- **Output**: Direct logit prediction
- **Interpretation**: Learned interaction scoring between node pairs

### `build_adj_mask` Function Analysis

**Location**: Lines 199-209

#### Purpose
Creates causal attention mask to restrict which nodes can attend to which others.

#### Implementation Details
```python
def build_adj_mask(edge_index, num_nodes, device):
    src, dst = edge_index                        # Extract source/destination
    idx = dst * num_nodes + src                  # Flatten to 1D indices  
    mask = torch.zeros(num_nodes * num_nodes,    # Create flat boolean mask
                       dtype=torch.bool, device=device)
    mask.index_fill_(0, idx, True)               # Mark existing edges as True
    
    # Remove self-loops (diagonal elements)
    diag_idx = torch.arange(num_nodes, device=device)
    mask.index_fill_(0, diag_idx * (num_nodes + 1), False)
    
    return mask.view(1, num_nodes, num_nodes)    # Reshape to (1, N, N)
```

#### Key Properties
- **Causality**: Only existing edges allow attention flow
- **Anti-reflexive**: Self-attention explicitly disabled (no diagonal)
- **Efficiency**: Uses flat indexing trick to avoid nested loops
- **Broadcasting**: `(1, N, N)` shape broadcasts over attention heads

### Causal Masking Semantics

#### Attention Masking Process
**Location**: Lines 184-186 in CausalAttention.forward()

```python
# mask out disallowed edges
mask = adj_mask.unsqueeze(1)                     # (1,N,N) → (1,1,N,N) 
attn_logits = attn_logits.masked_fill(~mask, float("-inf"))
```

#### Semantic Interpretation
- **True entries**: Node j CAN causally influence node i (edge exists)
- **False entries**: No causal connection → attention weight = 0 after softmax
- **Graph structure**: Attention respects original graph topology
- **Information flow**: Prevents "shortcuts" through non-existent edges

#### Mathematical Effect
```
Before masking: attn_logits[i,j] = (q_i · k_j) / √d
After masking:  attn_logits[i,j] = -∞ if edge (j→i) doesn't exist
After softmax:  α[i,j] = 0 if edge (j→i) doesn't exist
```

## Message Flow Sequence Diagrams

### Fast Link Prediction Path

```
Training Loop                LinkLoader              RiemannCausalGNN._score_edges
     │                           │                              │
     │────── next(batch) ────────▶│                              │
     │                           │                              │
     │                           │── sample subgraph ──────────│
     │                           │── generate neg edges ───────│
     │                           │── create batch.edge_label ──│
     │                           │                              │
     │◀───── batch ──────────────│                              │
     │                           │                              │
     │────── forward(batch, explain=False) ────────────────────▶│
     │                           │                              │
     │                           │                              ├─ 1. Encode: x → h 
     │                           │                              ├─ 2. HGCN Layer 1: h → h
     │                           │                              ├─ 3. HGCN Layer 2: h → h  
     │                           │                              ├─ 4. Build causal mask
     │                           │                              ├─ 5. Causal attention
     │                           │                              ├─ 6. Skip connection
     │                           │                              ├─ 7. Link scoring
     │                           │                              │   (hyperbolic/MLP)
     │◀─────────── logits ──────────────────────────────────────│
     │                           │                              │
     ├─ loss = BCEWithLogits(logits, labels)                   │
     ├─ loss.backward()                                         │
     ├─ optimizer.step()                                        │
```

### Causal Attention Detail Flow

```
RiemannCausalGNN._score_edges    build_adj_mask    CausalAttention.forward
         │                            │                       │
         ├─── N ≤ 4096? ──────────────┤                       │
         │                            │                       │
         ├─── build_adj_mask(edges) ──▶                       │
         │                            ├─ Flat index trick    │
         │                            ├─ Create bool mask    │
         │                            ├─ Remove diagonal     │
         │◀─── adj_mask(1,N,N) ───────┤                       │
         │                            │                       │
         ├─── z_tan = logmap0(h) ─────┤                       │
         ├─── causal_head(z_tan, mask) ───────────────────────▶│
         │                            │                       ├─ Q,K,V projections
         │                            │                       ├─ Attention logits
         │                            │                       ├─ Apply causal mask
         │                            │                       ├─ Softmax → α weights  
         │                            │                       ├─ Weighted sum
         │◀─────── causal_out, α ──────────────────────────────│
         │                            │                       │
         ├─ h = h + causal_out (skip connection)              │
         ├─ Link scoring (hyperbolic/MLP)                     │
```

### Edge Batch Processing Flow

```
Dataset        NeighborSampler           LinkLoader              Model
   │                  │                      │                    │
   ├─ train_edges ────▶                     │                    │
   │                  ├─ Create sampler ────▶                    │
   │                  │   fan_out=[5,3,3]   │                    │
   │                  │                      │                    │
   │                  │                      ├─ Batch creation   │
   │                  │                      │   ├─ Sample subgraph around edges
   │                  │                      │   ├─ NegativeSampling(1:1 ratio)
   │                  │                      │   ├─ edge_label_index: (2, 2P)
   │                  │                      │   ├─ edge_label: (2P,) [1,1,...,0,0]
   │                  │                      │                    │
   │                  │                      ├─── batch ─────────▶│
   │                  │                      │                    ├─ Extract pairs
   │                  │                      │                    ├─ Score each pair
   │                  │                      │                    │   src,dst = edge_pairs
   │                  │                      │                    │   logits = score(h[src], h[dst])
   │                  │                      │◀─── logits ───────│
```

## Architecture Characteristics

- **Hybrid Approach**: Combines Euclidean (attention) and hyperbolic (manifold) geometries
- **Dual Path Design**: Fast inference path vs. detailed explanation path
- **Compilation Ready**: Uses torch.compile for performance optimization
- **Memory Efficient**: Implements gradient scaling and memory management
- **Explanation Capable**: Provides interpretable path analysis for predictions
- **Production Ready**: Includes comprehensive validation, checkpointing, and profiling
- **Causal Structure**: Respects graph topology through attention masking
- **Multi-Modal Scoring**: Supports both geometric and learned similarity measures

