#!/usr/bin/env python3
"""
Environment Setup Script for kaggle3.py
Checks for required dependencies and provides installation guidance.
"""

import sys
import subprocess
import importlib.util

def check_package(package_name, import_name=None):
    """Check if a package is installed."""
    if import_name is None:
        import_name = package_name
    
    try:
        if import_name == "sklearn":
            import sklearn
        elif import_name == "torch_geometric":
            import torch_geometric
        else:
            importlib.import_module(import_name)
        return True
    except ImportError:
        return False

def get_gpu_info():
    """Get GPU information."""
    try:
        result = subprocess.run(['nvidia-smi', '--query-gpu=name,memory.total', '--format=csv,noheader,nounits'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            return result.stdout.strip()
        else:
            return "NVIDIA GPU detected but nvidia-smi failed"
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return "No NVIDIA GPU or nvidia-smi not available"

def main():
    print("🔧 kaggle3.py Environment Setup Check")
    print("=" * 50)
    
    # Core dependencies
    core_deps = [
        ("torch", "torch"),
        ("torch-geometric", "torch_geometric"), 
        ("scikit-learn", "sklearn"),
        ("pandas", "pandas"),
        ("numpy", "numpy"),
        ("geoopt", "geoopt"),
        ("networkx", "networkx"),
        ("matplotlib", "matplotlib")
    ]
    
    # Optional but recommended
    optional_deps = [
        ("pyarrow", "pyarrow"),
        ("fastparquet", "fastparquet"),
        ("intel-extension-for-pytorch", "intel_extension_for_pytorch")
    ]
    
    print("📦 Checking Core Dependencies:")
    missing_core = []
    for pkg_name, import_name in core_deps:
        if check_package(pkg_name, import_name):
            print(f"  ✅ {pkg_name}")
        else:
            print(f"  ❌ {pkg_name}")
            missing_core.append(pkg_name)
    
    print("\\n📦 Checking Optional Dependencies:")
    missing_optional = []
    for pkg_name, import_name in optional_deps:
        if check_package(pkg_name, import_name):
            print(f"  ✅ {pkg_name}")
        else:
            print(f"  ⚠️  {pkg_name} (optional)")
            missing_optional.append(pkg_name)
    
    print("\\n🖥️  System Information:")
    print(f"  Python Version: {sys.version}")
    gpu_info = get_gpu_info()
    print(f"  GPU: {gpu_info}")
    
    if missing_core:
        print("\\n❗ Missing Core Dependencies:")
        print("Install with pip:")
        print(f"  pip install {' '.join(missing_core)}")
        print("\\nOr install with conda:")
        print("  conda env create -f environment.yml")
        return False
    
    if missing_optional:
        print("\\n💡 Optional Dependencies (recommended):")
        print(f"  pip install {' '.join(missing_optional)}")
    
    print("\\n✅ Environment setup check complete!")
    
    # Quick import test
    try:
        print("\\n🧪 Testing critical imports...")
        import torch
        import torch_geometric
        import geoopt
        print(f"  PyTorch: {torch.__version__}")
        print(f"  PyG: {torch_geometric.__version__}")
        print(f"  CUDA Available: {torch.cuda.is_available()}")
        print("  ✅ All critical imports successful!")
        return True
    except Exception as e:
        print(f"  ❌ Import test failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
