#!/usr/bin/env python3
"""
Step 3: Scalability & Memory Profiling

This script executes the pipeline on progressively larger subsamples to:
- Ensure pipeline correctness on small data
- Record GPU memory (peak/allocated) and per-iteration wall-time
- Track frequency of causal-attention bypass (N ≤ 4096 threshold)
- Detect OOM or long-tail runtime spikes
- Document bottlenecks for architecture and brainstorm docs
"""

import torch
import torch.nn.functional as F
from torch import nn
from torch_geometric.data import Data
from torch_geometric.loader import LinkLoader
from torch_geometric.sampler import NeighborSampler, NegativeSampling
import pandas as pd
import numpy as np
import json
import time
import gc
import os
import psutil
import argparse
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

# Import the model and utilities from kaggle3.py
from kaggle3 import (
    RiemannCausalGNN, 
    forward_args,
    edge_batch_to_tensor,
    load_real_data,
    assert_batch_consistent
)

@dataclass
class ProfilingResults:
    """Container for profiling metrics"""
    scale_percent: float
    num_nodes: int
    num_edges: int
    num_features: int
    
    # Memory metrics
    gpu_memory_allocated_mb: float
    gpu_memory_reserved_mb: float
    gpu_memory_peak_mb: float
    cpu_memory_used_mb: float
    cpu_memory_peak_mb: float
    
    # Timing metrics
    forward_time_ms: float
    backward_time_ms: float
    total_iteration_time_ms: float
    
    # Model-specific metrics
    causal_attention_bypasses: int
    causal_attention_total: int
    bypass_frequency: float
    
    # Performance indicators
    oom_occurred: bool
    runtime_spike_detected: bool
    peak_subgraph_size: int
    
    # Bottleneck indicators
    bottleneck_type: str  # "memory", "compute", "sampling", "none"
    bottleneck_details: str

class ScalabilityProfiler:
    """Handles scalability profiling across different data scales"""
    
    def __init__(self, device: str = "auto"):
        self.device = torch.device("cuda" if torch.cuda.is_available() and device != "cpu" else "cpu")
        self.results: List[ProfilingResults] = []
        self.baseline_cpu_memory = self._get_cpu_memory_usage()
        
        print(f"Profiler initialized on device: {self.device}")
        if self.device.type == "cuda":
            print(f"GPU: {torch.cuda.get_device_name(0)}")
            print(f"GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    def _get_cpu_memory_usage(self) -> float:
        """Get current CPU memory usage in MB"""
        process = psutil.Process(os.getpid())
        return process.memory_info().rss / 1024 / 1024
    
    def _get_gpu_memory_stats(self) -> Tuple[float, float, float]:
        """Get GPU memory stats in MB"""
        if self.device.type != "cuda":
            return 0.0, 0.0, 0.0
        
        allocated = torch.cuda.memory_allocated(self.device) / 1024 / 1024
        reserved = torch.cuda.memory_reserved(self.device) / 1024 / 1024
        peak = torch.cuda.max_memory_allocated(self.device) / 1024 / 1024
        
        return allocated, reserved, peak
    
    def _subsample_data(self, data: Data, scale_percent: float) -> Data:
        """Create a subsample of the original data"""
        if scale_percent >= 100.0:
            return data
        
        # Calculate target number of nodes
        target_nodes = max(100, int(data.num_nodes * scale_percent / 100))
        
        # Randomly sample node indices
        if target_nodes >= data.num_nodes:
            return data
        
        node_indices = torch.randperm(data.num_nodes)[:target_nodes]
        
        # Create mapping from old to new indices
        node_mapping = {old_idx.item(): new_idx for new_idx, old_idx in enumerate(node_indices)}
        
        # Filter edges that exist between sampled nodes
        edge_mask = (
            torch.isin(data.edge_index[0], node_indices) & 
            torch.isin(data.edge_index[1], node_indices)
        )
        
        # Remap edge indices
        filtered_edges = data.edge_index[:, edge_mask]
        new_edge_index = torch.zeros_like(filtered_edges)
        
        for i, (src, dst) in enumerate(filtered_edges.t()):
            new_edge_index[0, i] = node_mapping[src.item()]
            new_edge_index[1, i] = node_mapping[dst.item()]
        
        # Create subsampled data
        subsampled_data = Data(
            x=data.x[node_indices],
            edge_index=new_edge_index,
            num_nodes=target_nodes,
            node_type=data.node_type[node_indices] if hasattr(data, 'node_type') else None
        )
        
        return subsampled_data
    
    def _create_small_test_data(self) -> Data:
        """Create intentionally small data for pipeline correctness test"""
        n_nodes = 100
        n_edges = 1000
        n_features = 100
        
        # Create random features
        x = torch.randn(n_nodes, n_features)
        
        # Create random edges (ensuring connectivity)
        edge_index = torch.randint(0, n_nodes, (2, n_edges))
        
        # Add some guaranteed connectivity
        for i in range(min(50, n_nodes-1)):
            edge_index[0, i] = i
            edge_index[1, i] = i + 1
        
        # Create node types
        node_type = torch.randint(0, 3, (n_nodes,))
        
        data = Data(x=x, edge_index=edge_index, num_nodes=n_nodes, node_type=node_type)
        return data
    
    def _setup_model_and_loaders(self, data: Data, batch_size: int = 1024) -> Tuple[RiemannCausalGNN, LinkLoader]:
        """Setup model and data loaders for the given data"""
        # Create model
        model = RiemannCausalGNN(
            in_channels=data.x.size(1), 
            hidden_channels=64, 
            num_vars=data.num_nodes
        ).to(self.device)
        
        # Create edges for train/test split
        edges_df = pd.DataFrame({
            'x_index': data.edge_index[0].cpu().numpy(),
            'y_index': data.edge_index[1].cpu().numpy()
        })
        
        from sklearn.model_selection import train_test_split
        train_edges, _ = train_test_split(edges_df, test_size=0.2, random_state=42)
        train_edge_index = edge_batch_to_tensor(train_edges)
        
        # Create data loader
        edge_sampler = NeighborSampler(
            data,
            num_neighbors=[5, 3, 3],
            subgraph_type='induced',
            replace=False,
            disjoint=False,
            directed=False,
        )
        
        train_loader = LinkLoader(
            data,
            link_sampler=edge_sampler,
            edge_label_index=train_edge_index,
            batch_size=min(batch_size, len(train_edges)),
            neg_sampling=NegativeSampling('binary', amount=1.0),
            shuffle=True,
            num_workers=0,
            persistent_workers=False,
        )
        
        return model, train_loader
    
    def _profile_single_iteration(self, model: RiemannCausalGNN, batch: Data) -> Dict:
        """Profile a single forward/backward pass"""
        # Reset GPU memory stats
        if self.device.type == "cuda":
            torch.cuda.reset_peak_memory_stats(self.device)
        
        # CPU memory before
        cpu_mem_before = self._get_cpu_memory_usage()
        
        # Prepare batch
        batch = batch.to(self.device, non_blocking=True)
        
        # Monitor causal attention bypasses
        bypass_count = 0
        total_calls = 0
        
        # Monkey patch to count bypasses
        original_score_edges = model._score_edges
        def counting_score_edges(*args, **kwargs):
            nonlocal bypass_count, total_calls
            x_local, edge_index_local, edge_pairs = args
            N = x_local.size(0)
            total_calls += 1
            if N <= 4096:
                bypass_count += 1
            return original_score_edges(*args, **kwargs)
        
        model._score_edges = counting_score_edges
        
        try:
            # Forward pass timing
            start_time = time.time()
            
            with torch.amp.autocast(device_type=self.device.type, dtype=torch.float16):
                y_pred, _, _ = model(*forward_args(batch), explain=False)
            
            forward_time = (time.time() - start_time) * 1000
            
            # Backward pass timing
            criterion = nn.BCEWithLogitsLoss()
            y_true = batch.edge_label.float()
            loss = criterion(y_pred, y_true)
            
            backward_start = time.time()
            loss.backward()
            backward_time = (time.time() - backward_start) * 1000
            
            total_time = forward_time + backward_time
            
            # Memory stats
            gpu_allocated, gpu_reserved, gpu_peak = self._get_gpu_memory_stats()
            cpu_mem_after = self._get_cpu_memory_usage()
            cpu_mem_peak = max(cpu_mem_before, cpu_mem_after)
            
            # Peak subgraph size (approximate from batch)
            peak_subgraph_size = batch.x.size(0)
            
            return {
                'forward_time_ms': forward_time,
                'backward_time_ms': backward_time,
                'total_iteration_time_ms': total_time,
                'gpu_memory_allocated_mb': gpu_allocated,
                'gpu_memory_reserved_mb': gpu_reserved,
                'gpu_memory_peak_mb': gpu_peak,
                'cpu_memory_used_mb': cpu_mem_after - self.baseline_cpu_memory,
                'cpu_memory_peak_mb': cpu_mem_peak - self.baseline_cpu_memory,
                'causal_attention_bypasses': bypass_count,
                'causal_attention_total': total_calls,
                'bypass_frequency': bypass_count / max(total_calls, 1),
                'peak_subgraph_size': peak_subgraph_size,
                'oom_occurred': False,
            }
            
        except torch.cuda.OutOfMemoryError as e:
            return {
                'forward_time_ms': 0.0,
                'backward_time_ms': 0.0,
                'total_iteration_time_ms': 0.0,
                'gpu_memory_allocated_mb': 0.0,
                'gpu_memory_reserved_mb': 0.0,
                'gpu_memory_peak_mb': 0.0,
                'cpu_memory_used_mb': 0.0,
                'cpu_memory_peak_mb': 0.0,
                'causal_attention_bypasses': 0,
                'causal_attention_total': 0,
                'bypass_frequency': 0.0,
                'peak_subgraph_size': 0,
                'oom_occurred': True,
            }
        finally:
            # Restore original method
            model._score_edges = original_score_edges
            
            # Cleanup
            if 'loss' in locals():
                del loss
            del batch
            gc.collect()
            if self.device.type == "cuda":
                torch.cuda.empty_cache()
    
    def _detect_bottlenecks(self, result_metrics: Dict) -> Tuple[str, str]:
        """Detect performance bottlenecks based on metrics"""
        bottleneck_type = "none"
        bottleneck_details = "No significant bottlenecks detected"
        
        # Memory bottleneck detection
        if result_metrics['oom_occurred']:
            bottleneck_type = "memory"
            bottleneck_details = "GPU Out-of-Memory error occurred"
        elif result_metrics['gpu_memory_peak_mb'] > 5000:  # >5GB usage
            bottleneck_type = "memory"
            bottleneck_details = f"High GPU memory usage: {result_metrics['gpu_memory_peak_mb']:.1f}MB"
        elif result_metrics['cpu_memory_peak_mb'] > 8000:  # >8GB usage
            bottleneck_type = "memory"
            bottleneck_details = f"High CPU memory usage: {result_metrics['cpu_memory_peak_mb']:.1f}MB"
        
        # Compute bottleneck detection
        elif result_metrics['total_iteration_time_ms'] > 10000:  # >10s per iteration
            bottleneck_type = "compute"
            bottleneck_details = f"Long iteration time: {result_metrics['total_iteration_time_ms']:.1f}ms"
        elif result_metrics['forward_time_ms'] > 8000:  # >8s forward pass
            bottleneck_type = "compute"
            bottleneck_details = f"Slow forward pass: {result_metrics['forward_time_ms']:.1f}ms"
        
        # Sampling bottleneck (large subgraphs)
        elif result_metrics['peak_subgraph_size'] > 50000:
            bottleneck_type = "sampling"
            bottleneck_details = f"Large subgraph size: {result_metrics['peak_subgraph_size']} nodes"
        
        return bottleneck_type, bottleneck_details
    
    def _detect_runtime_spikes(self, result_metrics: Dict, previous_time: Optional[float] = None) -> bool:
        """Detect runtime spikes compared to previous measurements"""
        if previous_time is None:
            return False
        
        current_time = result_metrics['total_iteration_time_ms']
        spike_threshold = 3.0  # 3x slower than previous
        
        return current_time > (previous_time * spike_threshold)
    
    def profile_scale(self, data: Data, scale_percent: float, previous_time: Optional[float] = None) -> ProfilingResults:
        """Profile the pipeline at a specific scale"""
        print(f"\n=== Profiling at {scale_percent}% scale ===")
        
        # Subsample data
        if scale_percent < 100:
            subsampled_data = self._subsample_data(data, scale_percent)
        else:
            subsampled_data = data
        
        print(f"Data: {subsampled_data.num_nodes} nodes, {subsampled_data.num_edges} edges, {subsampled_data.x.size(1)} features")
        
        # Setup model and loaders
        try:
            model, train_loader = self._setup_model_and_loaders(subsampled_data)
            
            # Get a batch for profiling
            batch = next(iter(train_loader))
            
            # Profile single iteration
            result_metrics = self._profile_single_iteration(model, batch)
            
            # Detect bottlenecks
            bottleneck_type, bottleneck_details = self._detect_bottlenecks(result_metrics)
            
            # Detect runtime spikes
            runtime_spike_detected = self._detect_runtime_spikes(result_metrics, previous_time)
            
            # Create result object
            result = ProfilingResults(
                scale_percent=scale_percent,
                num_nodes=subsampled_data.num_nodes,
                num_edges=subsampled_data.num_edges,
                num_features=subsampled_data.x.size(1),
                **result_metrics,
                runtime_spike_detected=runtime_spike_detected,
                bottleneck_type=bottleneck_type,
                bottleneck_details=bottleneck_details
            )
            
            self.results.append(result)
            
            # Print summary
            self._print_scale_summary(result)
            
            return result
            
        except Exception as e:
            print(f"Error during profiling at {scale_percent}% scale: {e}")
            
            # Create error result
            result = ProfilingResults(
                scale_percent=scale_percent,
                num_nodes=subsampled_data.num_nodes if 'subsampled_data' in locals() else 0,
                num_edges=subsampled_data.num_edges if 'subsampled_data' in locals() else 0,
                num_features=subsampled_data.x.size(1) if 'subsampled_data' in locals() else 0,
                gpu_memory_allocated_mb=0.0,
                gpu_memory_reserved_mb=0.0,
                gpu_memory_peak_mb=0.0,
                cpu_memory_used_mb=0.0,
                cpu_memory_peak_mb=0.0,
                forward_time_ms=0.0,
                backward_time_ms=0.0,
                total_iteration_time_ms=0.0,
                causal_attention_bypasses=0,
                causal_attention_total=0,
                bypass_frequency=0.0,
                oom_occurred=True,
                runtime_spike_detected=False,
                peak_subgraph_size=0,
                bottleneck_type="error",
                bottleneck_details=str(e)
            )
            
            self.results.append(result)
            return result
    
    def _print_scale_summary(self, result: ProfilingResults):
        """Print a summary of profiling results for this scale"""
        print(f"  Timing: Forward={result.forward_time_ms:.1f}ms, Backward={result.backward_time_ms:.1f}ms, Total={result.total_iteration_time_ms:.1f}ms")
        print(f"  GPU Memory: Peak={result.gpu_memory_peak_mb:.1f}MB, Allocated={result.gpu_memory_allocated_mb:.1f}MB")
        print(f"  CPU Memory: Peak={result.cpu_memory_peak_mb:.1f}MB")
        print(f"  Causal Attention: {result.causal_attention_bypasses}/{result.causal_attention_total} bypasses ({result.bypass_frequency:.1%})")
        print(f"  Peak Subgraph Size: {result.peak_subgraph_size} nodes")
        if result.oom_occurred:
            print("  ⚠️  OOM ERROR OCCURRED")
        if result.runtime_spike_detected:
            print("  ⚠️  RUNTIME SPIKE DETECTED")
        if result.bottleneck_type != "none":
            print(f"  🔍 Bottleneck: {result.bottleneck_type} - {result.bottleneck_details}")
    
    def run_full_profile(self, use_real_data: bool = True) -> List[ProfilingResults]:
        """Run the complete scalability profiling workflow"""
        print("🚀 Starting Step 3: Scalability & Memory Profiling")
        print("=" * 60)
        
        # Step 1: Small test data for pipeline correctness
        print("\n📋 Step 1: Pipeline Correctness Test")
        small_data = self._create_small_test_data()
        small_result = self.profile_scale(small_data, scale_percent=0.1)  # Label as 0.1% for consistency
        
        if small_result.oom_occurred or small_result.bottleneck_type == "error":
            print("❌ Pipeline correctness test failed! Stopping profiling.")
            return self.results
        else:
            print("✅ Pipeline correctness test passed!")
        
        # Step 2: Load real data (if available)
        if use_real_data:
            try:
                print("\n📊 Loading real data...")
                real_data = load_real_data('nodes.csv', 'edges.csv', 'feature-embd2.csv')
                print(f"Real data loaded: {real_data.num_nodes} nodes, {real_data.num_edges} edges")
            except Exception as e:
                print(f"❌ Failed to load real data: {e}")
                print("📋 Using generated test data instead")
                # Create larger test data
                real_data = self._create_large_test_data()
        else:
            print("📋 Using generated test data")
            real_data = self._create_large_test_data()
        
        # Step 3: Profile at different scales
        scales = [1.0, 5.0, 10.0]  # 1%, 5%, 10%
        previous_time = small_result.total_iteration_time_ms
        
        for scale in scales:
            print(f"\n📈 Profiling at {scale}% scale...")
            result = self.profile_scale(real_data, scale, previous_time)
            previous_time = result.total_iteration_time_ms
            
            # Stop if we hit major issues
            if result.oom_occurred and scale < max(scales):
                print(f"🛑 OOM at {scale}% scale. Stopping further scaling tests.")
                break
        
        return self.results
    
    def _create_large_test_data(self) -> Data:
        """Create larger test data when real data is not available"""
        n_nodes = 10000
        n_edges = 50000
        n_features = 256
        
        print(f"Creating test data: {n_nodes} nodes, {n_edges} edges, {n_features} features")
        
        # Create random features
        x = torch.randn(n_nodes, n_features)
        
        # Create random edges with some structure
        edge_index = torch.randint(0, n_nodes, (2, n_edges))
        
        # Ensure connectivity by adding a spanning tree
        for i in range(n_nodes - 1):
            edge_index[0, i] = i
            edge_index[1, i] = (i + 1) % n_nodes
        
        # Create node types
        node_type = torch.randint(0, 5, (n_nodes,))
        
        data = Data(x=x, edge_index=edge_index, num_nodes=n_nodes, node_type=node_type)
        return data
    
    def generate_report(self, output_file: str = "scalability_report.md"):
        """Generate a detailed report of profiling results"""
        if not self.results:
            print("No profiling results to report")
            return
        
        report_lines = [
            "# Scalability & Memory Profiling Report",
            "",
            "Generated by Step 3: Scalability & Memory Profiling",
            "",
            "## Executive Summary",
            "",
        ]
        
        # Summary statistics
        successful_runs = [r for r in self.results if not r.oom_occurred and r.bottleneck_type != "error"]
        failed_runs = [r for r in self.results if r.oom_occurred or r.bottleneck_type == "error"]
        
        report_lines.extend([
            f"- **Successful runs**: {len(successful_runs)}/{len(self.results)}",
            f"- **Failed runs**: {len(failed_runs)} (OOM or errors)",
            f"- **Maximum scale tested**: {max(r.scale_percent for r in self.results):.1f}%",
            f"- **Device used**: {self.device}",
            "",
        ])
        
        if successful_runs:
            max_memory = max(r.gpu_memory_peak_mb for r in successful_runs)
            max_time = max(r.total_iteration_time_ms for r in successful_runs)
            avg_bypass_freq = np.mean([r.bypass_frequency for r in successful_runs])
            
            report_lines.extend([
                f"- **Peak GPU memory usage**: {max_memory:.1f} MB",
                f"- **Maximum iteration time**: {max_time:.1f} ms",
                f"- **Average causal attention bypass frequency**: {avg_bypass_freq:.1%}",
                "",
            ])
        
        # Bottlenecks summary
        bottlenecks = defaultdict(int)
        for result in self.results:
            bottlenecks[result.bottleneck_type] += 1
        
        if len(bottlenecks) > 1 or "none" not in bottlenecks:
            report_lines.extend([
                "### Detected Bottlenecks",
                "",
            ])
            for bt, count in sorted(bottlenecks.items()):
                if bt != "none":
                    report_lines.append(f"- **{bt.title()}**: {count} occurrences")
            report_lines.append("")
        
        # Detailed results table
        report_lines.extend([
            "## Detailed Results",
            "",
            "| Scale | Nodes | Edges | Features | GPU Peak (MB) | Iteration (ms) | Bypass Freq | Bottleneck | Status |",
            "|-------|-------|-------|----------|---------------|----------------|-------------|------------|---------|",
        ])
        
        for result in self.results:
            status = "❌ OOM" if result.oom_occurred else "❌ Error" if result.bottleneck_type == "error" else "✅ Success"
            bottleneck = result.bottleneck_type if result.bottleneck_type != "none" else "-"
            
            report_lines.append(
                f"| {result.scale_percent:.1f}% | {result.num_nodes:,} | {result.num_edges:,} | "
                f"{result.num_features} | {result.gpu_memory_peak_mb:.1f} | "
                f"{result.total_iteration_time_ms:.1f} | {result.bypass_frequency:.1%} | "
                f"{bottleneck} | {status} |"
            )
        
        report_lines.extend([
            "",
            "## Performance Analysis",
            "",
        ])
        
        # Performance trends
        if len(successful_runs) > 1:
            scales = [r.scale_percent for r in successful_runs]
            times = [r.total_iteration_time_ms for r in successful_runs]
            memories = [r.gpu_memory_peak_mb for r in successful_runs]
            
            # Simple linear scaling analysis
            time_scaling = times[-1] / times[0] if times[0] > 0 else float('inf')
            memory_scaling = memories[-1] / memories[0] if memories[0] > 0 else float('inf')
            data_scaling = scales[-1] / scales[0]
            
            report_lines.extend([
                f"- **Time scaling factor**: {time_scaling:.2f}x (data scaled {data_scaling:.2f}x)",
                f"- **Memory scaling factor**: {memory_scaling:.2f}x (data scaled {data_scaling:.2f}x)",
                "",
            ])
            
            if time_scaling > data_scaling * 1.5:
                report_lines.append("⚠️  **Time scaling is worse than linear** - potential compute bottleneck")
            if memory_scaling > data_scaling * 1.2:
                report_lines.append("⚠️  **Memory scaling is worse than linear** - potential memory bottleneck")
        
        # Causal attention analysis
        if successful_runs:
            bypass_freqs = [r.bypass_frequency for r in successful_runs]
            avg_bypass = np.mean(bypass_freqs)
            
            report_lines.extend([
                "",
                "### Causal Attention Analysis",
                "",
                f"- **Average bypass frequency**: {avg_bypass:.1%}",
            ])
            
            if avg_bypass > 0.8:
                report_lines.append("✅ **High bypass frequency** - most subgraphs are small enough for efficient attention")
            elif avg_bypass < 0.5:
                report_lines.append("⚠️  **Low bypass frequency** - many large subgraphs may cause memory issues")
            
            report_lines.extend([
                "",
                "The causal attention bypass threshold (N ≤ 4096) determines when the expensive attention computation is skipped.",
                "Higher bypass frequency indicates better scalability for this implementation.",
                "",
            ])
        
        # Recommendations
        report_lines.extend([
            "## Recommendations",
            "",
        ])
        
        if failed_runs:
            report_lines.append("### Immediate Issues")
            for result in failed_runs:
                if result.oom_occurred:
                    report_lines.append(f"- **OOM at {result.scale_percent}% scale**: Reduce batch size or use gradient accumulation")
                elif result.bottleneck_type == "error":
                    report_lines.append(f"- **Error at {result.scale_percent}% scale**: {result.bottleneck_details}")
            report_lines.append("")
        
        memory_bottlenecks = [r for r in self.results if r.bottleneck_type == "memory"]
        compute_bottlenecks = [r for r in self.results if r.bottleneck_type == "compute"]
        sampling_bottlenecks = [r for r in self.results if r.bottleneck_type == "sampling"]
        
        if memory_bottlenecks:
            report_lines.extend([
                "### Memory Optimization",
                "- Consider gradient checkpointing for large models",
                "- Implement gradient accumulation to reduce batch size",
                "- Use mixed precision training (already implemented)",
                "- Consider model sharding for very large graphs",
                "",
            ])
        
        if compute_bottlenecks:
            report_lines.extend([
                "### Compute Optimization",
                "- Profile individual operations to identify slow components",
                "- Consider torch.compile() for faster execution",
                "- Optimize attention mechanism for larger subgraphs",
                "- Investigate parallel processing opportunities",
                "",
            ])
        
        if sampling_bottlenecks:
            report_lines.extend([
                "### Sampling Optimization", 
                "- Reduce neighbor sampling fan-out to control subgraph size",
                "- Implement adaptive sampling based on node degree",
                "- Consider hierarchical sampling strategies",
                "",
            ])
        
        # Architecture considerations
        report_lines.extend([
            "## Architecture Considerations",
            "",
            "Based on the profiling results, the following architectural decisions should be considered:",
            "",
        ])
        
        if successful_runs:
            max_successful_scale = max(r.scale_percent for r in successful_runs)
            if max_successful_scale < 10.0:
                report_lines.extend([
                    "- **Scalability Concern**: Unable to process 10% of full data",
                    "- Consider implementing distributed training or model parallelism",
                    "- Investigate memory-efficient attention alternatives",
                    "",
                ])
            else:
                report_lines.extend([
                    f"- **Scalability Success**: Successfully processed up to {max_successful_scale}% of data",
                    "- Current architecture appears suitable for moderate-scale problems",
                    "",
                ])
        
        # Write report
        with open(output_file, 'w') as f:
            f.write('\n'.join(report_lines))
        
        print(f"📄 Detailed report generated: {output_file}")

def main():
    parser = argparse.ArgumentParser(description='Scalability profiling for Step 3')
    parser.add_argument('--use_real_data', action='store_true', help='Use real CSV data instead of synthetic data')
    parser.add_argument('--device', choices=['auto', 'cpu', 'cuda'], default='auto', help='Device to use for profiling')
    parser.add_argument('--output', default='scalability_report.md', help='Output report filename')
    args = parser.parse_args()
    
    # Create profiler
    profiler = ScalabilityProfiler(device=args.device)
    
    # Run profiling
    results = profiler.run_full_profile(use_real_data=args.use_real_data)
    
    # Generate report
    profiler.generate_report(args.output)
    
    print("\n" + "=" * 60)
    print("🎯 Step 3: Scalability & Memory Profiling COMPLETE")
    print("=" * 60)
    
    # Print summary
    successful = len([r for r in results if not r.oom_occurred and r.bottleneck_type != "error"])
    print(f"✅ Successful runs: {successful}/{len(results)}")
    
    if successful > 0:
        max_scale = max(r.scale_percent for r in results if not r.oom_occurred and r.bottleneck_type != "error")
        print(f"📈 Maximum successful scale: {max_scale:.1f}%")
        
        bottlenecks = set(r.bottleneck_type for r in results if r.bottleneck_type not in ["none", "error"])
        if bottlenecks:
            print(f"🔍 Bottlenecks detected: {', '.join(bottlenecks)}")
        else:
            print("✅ No significant bottlenecks detected")
    
    print(f"📄 Report saved to: {args.output}")

if __name__ == "__main__":
    main()
