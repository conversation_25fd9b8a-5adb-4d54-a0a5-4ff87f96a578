Loading torch-geometric dataset  OGB_MAG (preprocess=metapath2vec) …
Using existing file mag.zip
Extracting kaggle/working/data/mag/raw/mag.zip
Downloading https://data.pyg.org/datasets/mag_metapath2vec_emb.zip
Extracting kaggle/working/data/mag/raw/mag_metapath2vec_emb.zip
Processing...
Done!
done1  (N=1,939,743,  E=10,792,672)
Loading data...
done2
Loading data...
done2
Starting samplers...
Starting samplers...
done Samplers
we start here compile
done Samplers
we start here compile
torch compile ends
torch compile ends
train model compiling
train model compiling
eval model compiling
eval model compiling
done compiling
Starting training...
done compiling
Starting training...
[rank0]:W0724 14:33:30.494000 726 torch/_inductor/utils.py:1137] [1/1_1] Not enough SMs to use max_autotune_gemm mode
[rank1]:W0724 14:33:31.228000 727 torch/_inductor/utils.py:1137] [1/1_1] Not enough SMs to use max_autotune_gemm mode
[W724 14:33:48.860260359 CudaIPCTypes.cpp:16] Producer process has been terminated before all shared CUDA tensors released. See Note [Sharing CUDA tensors]
I0724 14:33:49.040000 769 torch/_dynamo/eval_frame.py:398] TorchDynamo attempted to trace the following frames: [
I0724 14:33:49.040000 769 torch/_dynamo/eval_frame.py:398] 
I0724 14:33:49.040000 769 torch/_dynamo/eval_frame.py:398] ]
I0724 14:33:49.051000 769 torch/_dynamo/utils.py:446] TorchDynamo compilation metrics:
I0724 14:33:49.051000 769 torch/_dynamo/utils.py:446] Function    Runtimes (s)
I0724 14:33:49.051000 769 torch/_dynamo/utils.py:446] ----------  --------------
V0724 14:33:49.051000 769 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats constrain_symbol_range: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.052000 769 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats defer_runtime_assert: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:49.052000 769 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats evaluate_expr: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:49.052000 769 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _simplify_floor_div: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.053000 769 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _maybe_guard_rel: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:49.053000 769 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _find: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.053000 769 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats has_hint: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:49.054000 769 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats size_hint: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:49.054000 769 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats simplify: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.054000 769 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _update_divisible: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.054000 769 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats replace: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.055000 769 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _maybe_evaluate_static: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.055000 769 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats get_implications: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.055000 769 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats get_axioms: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.055000 769 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _maybe_evaluate_static_worker: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.056000 769 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats safe_expand: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:49.056000 769 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats uninteresting_files: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
W0724 14:33:49.083000 714 torch/multiprocessing/spawn.py:169] Terminating process 727 via signal SIGTERM
I0724 14:33:49.358000 849 torch/_dynamo/eval_frame.py:398] TorchDynamo attempted to trace the following frames: [
I0724 14:33:49.358000 849 torch/_dynamo/eval_frame.py:398] 
I0724 14:33:49.358000 849 torch/_dynamo/eval_frame.py:398] ]
I0724 14:33:49.365000 849 torch/_dynamo/utils.py:446] TorchDynamo compilation metrics:
I0724 14:33:49.365000 849 torch/_dynamo/utils.py:446] Function    Runtimes (s)
I0724 14:33:49.365000 849 torch/_dynamo/utils.py:446] ----------  --------------
V0724 14:33:49.366000 849 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats constrain_symbol_range: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.366000 849 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats defer_runtime_assert: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:49.366000 849 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats evaluate_expr: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:49.367000 849 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _simplify_floor_div: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.367000 849 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _maybe_guard_rel: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:49.367000 849 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _find: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.367000 849 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats has_hint: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:49.368000 849 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats size_hint: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:49.368000 849 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats simplify: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.368000 849 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _update_divisible: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.368000 849 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats replace: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.369000 849 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _maybe_evaluate_static: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.369000 849 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats get_implications: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.369000 849 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats get_axioms: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.369000 849 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _maybe_evaluate_static_worker: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.370000 849 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats safe_expand: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:49.370000 849 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats uninteresting_files: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
I0724 14:33:49.433000 767 torch/_dynamo/eval_frame.py:398] TorchDynamo attempted to trace the following frames: [
I0724 14:33:49.433000 767 torch/_dynamo/eval_frame.py:398] 
I0724 14:33:49.433000 767 torch/_dynamo/eval_frame.py:398] ]
I0724 14:33:49.444000 767 torch/_dynamo/utils.py:446] TorchDynamo compilation metrics:
I0724 14:33:49.444000 767 torch/_dynamo/utils.py:446] Function    Runtimes (s)
I0724 14:33:49.444000 767 torch/_dynamo/utils.py:446] ----------  --------------
V0724 14:33:49.445000 767 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats constrain_symbol_range: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.445000 767 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats defer_runtime_assert: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:49.446000 767 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats evaluate_expr: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:49.446000 767 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _simplify_floor_div: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.447000 767 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _maybe_guard_rel: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:49.447000 767 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _find: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.448000 767 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats has_hint: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:49.448000 767 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats size_hint: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:49.449000 767 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats simplify: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.449000 767 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _update_divisible: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.449000 767 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats replace: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.450000 767 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _maybe_evaluate_static: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.450000 767 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats get_implications: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.451000 767 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats get_axioms: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.451000 767 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _maybe_evaluate_static_worker: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:49.451000 767 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats safe_expand: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:49.452000 767 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats uninteresting_files: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
Traceback (most recent call last):
  File "/kaggle/input/kaggle7/kaggle7.py", line 899, in <module>
    torch.multiprocessing.spawn(
  File "/usr/local/lib/python3.11/dist-packages/torch/multiprocessing/spawn.py", line 340, in spawn
    return start_processes(fn, args, nprocs, join, daemon, start_method="spawn")
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/multiprocessing/spawn.py", line 296, in start_processes
    while not context.join():
              ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/multiprocessing/spawn.py", line 215, in join
    raise ProcessRaisedException(msg, error_index, failed_process.pid)
torch.multiprocessing.spawn.ProcessRaisedException: 

-- Process 0 terminated with the following error:
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/dist-packages/torch/multiprocessing/spawn.py", line 90, in _wrap
    fn(i, *args)
  File "/kaggle/input/kaggle7/kaggle7.py", line 798, in safe_main_worker
    main_worker(rank, world_size)
  File "/kaggle/input/kaggle7/kaggle7.py", line 737, in main_worker
    y_pred, *_ = model(*forward_args(batch), explain=False)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/nn/modules/module.py", line 1750, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/nn/parallel/distributed.py", line 1643, in forward
    else self._run_ddp_forward(*inputs, **kwargs)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/nn/parallel/distributed.py", line 1459, in _run_ddp_forward
    return self.module(*inputs, **kwargs)  # type: ignore[index]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/nn/modules/module.py", line 1750, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/kaggle/input/kaggle7/kaggle7.py", line 491, in forward
    logits = self._score_edges(x_local, edge_index_local, edge_pairs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/_dynamo/eval_frame.py", line 574, in _fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/kaggle/input/kaggle7/kaggle7.py", line 651, in _score_edges_fp16
    return orig(*a, **kw)
           ^^^^^^^^^^^^^^
  File "/kaggle/input/kaggle7/kaggle7.py", line 291, in _score_edges
    def _score_edges(self, x_local, edge_index_local, edge_pairs):
  File "/usr/local/lib/python3.11/dist-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/nn/modules/module.py", line 1750, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/kaggle/input/kaggle7/kaggle7.py", line 255, in forward
    def forward(self, x, edge_index):
  File "/kaggle/input/kaggle7/kaggle7.py", line 256, in torch_dynamo_resume_in_forward_at_256
    h = self.manifold.expmap0(x)
  File "/tmp/__mp_main___HyperbolicGCN_propagate_qyd5mnem.py", line 102, in propagate
    def propagate(
  File "/tmp/__mp_main___HyperbolicGCN_propagate_qyd5mnem.py", line 121, in torch_dynamo_resume_in_propagate_at_121
    mutable_size = self._check_input(edge_index, size)
  File "/tmp/__mp_main___HyperbolicGCN_propagate_qyd5mnem.py", line 122, in torch_dynamo_resume_in_propagate_at_122
    fuse = is_sparse(edge_index) and self.fuse
  File "/tmp/__mp_main___HyperbolicGCN_propagate_qyd5mnem.py", line 129, in torch_dynamo_resume_in_propagate_at_129
    kwargs = self.collect(
  File "/kaggle/input/kaggle7/kaggle7.py", line 259, in message
    def message(self, x_j):
  File "/kaggle/input/kaggle7/kaggle7.py", line 260, in torch_dynamo_resume_in_message_at_260
    tangent = self.manifold.logmap0(x_j)
  File "/usr/local/lib/python3.11/dist-packages/geoopt/manifolds/stereographic/manifold.py", line 326, in expmap0
    @__scaling__(ScalingInfo(u=-1))
  File "/usr/local/lib/python3.11/dist-packages/geoopt/manifolds/stereographic/manifold.py", line 328, in torch_dynamo_resume_in_expmap0_at_328
    res = math.expmap0(u, k=self.k, dim=dim)
  File "/usr/local/lib/python3.11/dist-packages/torch/_dynamo/eval_frame.py", line 745, in _fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/_functorch/aot_autograd.py", line 1184, in forward
    return compiled_fn(full_args)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 310, in runtime_wrapper
    all_outs = call_func_at_runtime_with_args(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/_functorch/_aot_autograd/utils.py", line 126, in call_func_at_runtime_with_args
    out = normalize_as_list(f(args))
                            ^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/_functorch/_aot_autograd/utils.py", line 100, in g
    return f(*args)
           ^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/autograd/function.py", line 575, in apply
    return super().apply(*args, **kwargs)  # type: ignore[misc]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 1585, in forward
    fw_outs = call_func_at_runtime_with_args(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/_functorch/_aot_autograd/utils.py", line 126, in call_func_at_runtime_with_args
    out = normalize_as_list(f(args))
                            ^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 490, in wrapper
    return compiled_fn(runtime_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 672, in inner_fn
    outs = compiled_fn(args)
           ^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/_inductor/output_code.py", line 466, in __call__
    return self.current_callable(inputs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/_inductor/compile_fx.py", line 1208, in run
    return compiled_fn(new_inputs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/_inductor/cudagraph_trees.py", line 398, in deferred_cudagraphify
    fn, out = cudagraphify(model, inputs, new_static_input_idxs, *args, **kwargs)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/_inductor/cudagraph_trees.py", line 428, in cudagraphify
    return manager.add_function(
           ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/_inductor/cudagraph_trees.py", line 2253, in add_function
    return fn, fn(inputs)
               ^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/_inductor/cudagraph_trees.py", line 1947, in run
    out = self._run(new_inputs, function_id)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/_inductor/cudagraph_trees.py", line 2055, in _run
    out = self.run_eager(new_inputs, function_id)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/_inductor/cudagraph_trees.py", line 2219, in run_eager
    return node.run(new_inputs)
           ^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/torch/_inductor/cudagraph_trees.py", line 643, in run
    out = self.wrapped_function.model(new_inputs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/tmp/torchinductor_root/j3/cj3wkicp5ewjoygu3exgiqvohoxogczb6u6vm3ozw5txn2tlpk74.py", line 183, in call
    buf3 = empty_strided_cuda((s0, s1), (s1, 1), torch.float32)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 366.00 MiB. GPU 0 has a total capacity of 14.74 GiB of which 2.30 GiB is free. Process 23209 has 100.00 MiB memory in use. Process 23208 has 11.95 GiB memory in use. Process 23728 has 100.00 MiB memory in use. Process 23847 has 100.00 MiB memory in use. Process 23870 has 100.00 MiB memory in use. Process 24009 has 100.00 MiB memory in use. 11.79 GiB allowed; Of the allocated memory 6.75 GiB is allocated by PyTorch, with 2.49 GiB allocated in private pools (e.g., CUDA Graphs), and 4.98 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)

I0724 14:33:50.935000 871 torch/_dynamo/eval_frame.py:398] TorchDynamo attempted to trace the following frames: [
I0724 14:33:50.935000 871 torch/_dynamo/eval_frame.py:398] 
I0724 14:33:50.935000 871 torch/_dynamo/eval_frame.py:398] ]
I0724 14:33:50.940000 871 torch/_dynamo/utils.py:446] TorchDynamo compilation metrics:
I0724 14:33:50.940000 871 torch/_dynamo/utils.py:446] Function    Runtimes (s)
I0724 14:33:50.940000 871 torch/_dynamo/utils.py:446] ----------  --------------
V0724 14:33:50.941000 871 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats constrain_symbol_range: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:50.941000 871 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats defer_runtime_assert: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:50.941000 871 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats evaluate_expr: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:50.941000 871 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _simplify_floor_div: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:50.942000 871 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _maybe_guard_rel: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:50.942000 871 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _find: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:50.942000 871 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats has_hint: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:50.942000 871 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats size_hint: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:50.942000 871 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats simplify: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:50.943000 871 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _update_divisible: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:50.943000 871 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats replace: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:50.943000 871 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _maybe_evaluate_static: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:50.943000 871 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats get_implications: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:50.944000 871 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats get_axioms: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:50.944000 871 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats _maybe_evaluate_static_worker: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
V0724 14:33:50.944000 871 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats safe_expand: CacheInfo(hits=0, misses=0, maxsize=256, currsize=0)
V0724 14:33:50.944000 871 torch/fx/experimental/symbolic_shapes.py:172] lru_cache_stats uninteresting_files: CacheInfo(hits=0, misses=0, maxsize=None, currsize=0)
