#!/usr/bin/env python3

import torch
from torch_geometric.data import Data
import pandas as pd
import numpy as np
import json
import argparse

def load_real_data(nodes_csv, edges_csv, features_csv):
    """Load real data from CSV files"""
    print(f"Loading nodes from {nodes_csv}...")
    nodes_df = pd.read_csv(nodes_csv)
    node_type_mapping = {n: i for i, n in enumerate(nodes_df['node_type'].unique())}
    node_type = torch.tensor(nodes_df['node_type'].map(node_type_mapping).values, dtype=torch.long)
    num_nodes = len(nodes_df)
    print(f"Loaded {num_nodes} nodes with {len(node_type_mapping)} types")

    print(f"Loading features from {features_csv}...")
    features_df = pd.read_csv(features_csv, low_memory=False)
    features = np.array([json.loads(e) for e in features_df['embedding']])
    x = torch.tensor(features, dtype=torch.float)
    print(f"Features shape: {x.shape}")

    print(f"Loading edges from {edges_csv}...")
    edges_df = pd.read_csv(edges_csv)
    edge_index = torch.tensor(edges_df[['x_index', 'y_index']].to_numpy().T, dtype=torch.long)
    print(f"Edges shape: {edge_index.shape}")

    # Create data object
    data = Data(x=x, edge_index=edge_index, num_nodes=num_nodes, node_type=node_type)
    print(f"Data object created with {data.num_nodes} nodes, {data.num_edges} edges")
    
    return data

def main():
    parser = argparse.ArgumentParser(description='Test real data loading')
    parser.add_argument('--use_real_data', action='store_true', help='Use real data instead of dummy data')
    args = parser.parse_args()

    if args.use_real_data:
        print("Testing real data loading...")
        data = load_real_data('nodes.csv', 'edges.csv', 'feature-embd2.csv')
        print(f"Successfully loaded real data:")
        print(f"  Nodes: {data.num_nodes}")
        print(f"  Edges: {data.num_edges}")
        print(f"  Feature dimensions: {data.x.size(1)}")
        print(f"  Node types: {data.node_type.unique()}")
    else:
        print("Testing dummy data creation...")
        n_nodes = 100
        dummy_node_types = ['type1', 'type2', 'type3']
        dummy_type_id = np.random.randint(0, 3, size=n_nodes)
        node_type = torch.tensor(dummy_type_id, dtype=torch.long)
        
        # Create dummy edge data
        n_edges = 300
        dummy_edge_index = np.random.randint(0, n_nodes, size=(2, n_edges))
        edge_index = torch.tensor(dummy_edge_index, dtype=torch.long)

        # Create dummy feature data
        dummy_emb_arr = np.random.rand(n_nodes, 16)
        x = torch.tensor(dummy_emb_arr, dtype=torch.float)

        data = Data(x=x, edge_index=edge_index, num_nodes=n_nodes, node_type=node_type)
        print(f"Successfully created dummy data:")
        print(f"  Nodes: {data.num_nodes}")
        print(f"  Edges: {data.num_edges}")
        print(f"  Feature dimensions: {data.x.size(1)}")
        print(f"  Node types: {data.node_type.unique()}")

if __name__ == "__main__":
    main()
