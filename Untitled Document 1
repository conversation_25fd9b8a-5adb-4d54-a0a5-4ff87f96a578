1. Bootstrap & Environment Setup
• Check if you need to add any extra dependencies to run kaggle3.py file.<br/>
• Verify GPU/CPU availability and record hardware specs for later scalability notes.
2. Data-Loading Refactor
• Implement a new `load_real_data()` helper in `kaggle3.py` that:<br/>
  – Reads node metadata and features (≈129 k × 384) from the CSVs.<br/>
  – Converts edges (≈4 M) to `edge_index` tensor.<br/>
  – Builds `Data` object with correct `num_nodes`, `x`, and optional `node_type` fields.<br/>
• Replace the current dummy-data block with this loader and add CLI flag `--use_real_data` for easy switching.
3. Scalability & Memory Profiling
• Run the script on a small subsample (e.g., 100 nodes / 1 k edges / 100 features) to ensure functionality.<br/>
• Incrementally scale to full graph while profiling:<br/>
  – GPU memory, forward/backward time, attention cut-off (`N ≤ 4096`) hits.<br/>
• Note bottlenecks (attention O(N²), subgraph explosion, etc.) for later documentation and brainstorm.
4. Tensor Dimension Trace Collection
• Insert lightweight `print` / `torch.Size` logging hooks or use PyTorch Profiler shapes mode.<br/>
• Capture actual tensor shapes for the real dataset through one full forward pass.<br/>
• Store results for inclusion in `architecture.md` (update F=384, realistic N/E ranges, batch sizes, etc.).
5. Generate architecture.md
• Draft the file following required sections:<br/>
  – High-level overview, major components, dependency graph.<br/>
  – External libs list.<br/>
  – Detailed tensor dimension trace (using data from previous step).<br/>
  – Deep dive into link-prediction & causal attention logic (edge pipeline, hyperbolic vs MLP scoring, build_adj_mask explanation).<br/>
  – Message-flow sequence diagrams (fast path, causal attention, edge batching).<br/>
  – Key architecture characteristics & takeaways.
6. Static & Dynamic Bug Hunt
• Perform static code review for logical / resource issues.<br/>
• Run unit-like tests and full training dry-run; capture exceptions, incorrect metrics, CUDA/CPU guards, etc.<br/>
• For each discovered bug:<br/>
  – Isolate minimal repro.<br/>
  – Write and test the patch.<br/>
  – Record description, root cause, fixed snippet, and criticality for `bugfix_reason.md`.
7. Write bugfix_reason.md
• Structure per requirements: bug description → root cause → fix code snippet → patch summary table.<br/>
• Include any CUDA-guard patches, OOM preventions, metric mis-uses, etc., validated in prior step.
8. Research & Draft brainstorm.md
• Compile at least 4 well-researched enhancement ideas (e.g., Relation-Aware Hyperbolic GAT, Heterogeneous batching + Metapath2Vec pre-training, Curriculum negative sampling, Mixed-precision manifold ops).<br/>
• For each: list pros/cons, expected gains, complexity estimates, priority, and resource table.<br/>
• Tie suggestions to profiling observations and dataset scale challenges.
9. Quality Assurance & Final Validation
• Run end-to-end training for a few epochs on the full dataset with fixed code.<br/>
• Ensure no crashes, metrics logged, and checkpoint saved.<br/>
• Spell-check, ensure Markdown formatting, and cross-link between the three docs.
10. Deliverables Packaging
• Commit updated `kaggle3.py`, `architecture.md`, `bugfix_reason.md`, and `brainstorm.md`.<br/>
• Provide a short `README` explaining how to run with real data and reproduce results.<br/>
• Tag commit or create archive for hand-off to the user / next agent.
