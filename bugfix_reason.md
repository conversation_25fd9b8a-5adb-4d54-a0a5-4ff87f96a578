# Bug Fix Reasons

## Table of Contents
- [Bug #1: No Double `auroc.update` Call Found in `validate`](#bug-1-no-double-aurocupdate-call-found-in-validate)
- [Bug #2: Missing `hits_total` Increment in Loop](#bug-2-missing-hits_total-increment-in-loop)
- [Bug #3: Potential OOM in `explain_paths` for Large Subgraphs](#bug-3-potential-oom-in-explain_paths-for-large-subgraphs)
- [Bug #4: `torch.cuda.amp.autocast` Executed When CUDA Unavailable](#bug-4-torchcudaampautocast-executed-when-cuda-unavailable)
- [Bug #5: `assert_batch_consistent` Uses `.any(dim=0)` Incorrectly](#bug-5-assert_batch_consistent-uses-anydim0-incorrectly)
- [Patch Recommendations](#patch-recommendations)

---

*Related Documentation:*
- [← Model  26 Training Enhancements](brainstorm.md)
- [High-Level Architecture →](architecture.md)

---

## Bug #1: No Double `auroc.update` Call Found in `validate`
**Symptom ➜ Root Cause ➜ Fix**  
❌ Could not locate double auroc.update call - code uses s<PERSON><PERSON><PERSON>'s roc_auc_score directly rather than a metric object with update method.

## Bug #2: Missing `hits_total` Increment in Loop
**Symptom ➜ Root Cause ➜ Fix**  
✅ **Symptom**: `hits_total` is incorrectly placed inside the batch loop at line 506, causing it to be incremented multiple times per batch.  
**Root Cause**: Line 506 `hits_total += y_true.size(0)` is inside the validation loop instead of being calculated once per batch.  
**Fix Snippet**:
```python
# Move hits_total increment outside the k-loop but inside batch loop
for k in k_list:
    topk = prob.topk(k).indices
    hits_correct[k] += y_true[topk].sum().item()
# hits_total should be incremented here, not inside k-loop
hits_total += y_true.size(0)  # This is actually correct as written
```
**Note**: Upon further review, the current placement at line 506 is actually correct - it's outside the k-loop.

## Bug #3: Potential OOM in `explain_paths` for Large Subgraphs
**Symptom ➜ Root Cause ➜ Fix**  
✅ **Symptom**: OOM errors when processing large subgraphs in explain_paths method.  
**Root Cause**: Lines 321-324 use k_hop_subgraph with num_hops=4 without size limits, can create massive subgraphs.  
**Fix Snippet**:
```python
sub_nodes, sub_edge_index, mapping, _ = k_hop_subgraph(
    seeds, num_hops=4, edge_index=edge_index_local,
    relabel_nodes=True,
)
# Add size check after subgraph extraction
if sub_nodes.size(0) > 10000:  # Skip very large subgraphs
    continue
```

## Bug #4: `torch.cuda.amp.autocast` Executed When CUDA Unavailable
**Symptom ➜ Root Cause ➜ Fix**  
✅ **Symptom**: Runtime error when CUDA is unavailable but torch.cuda.amp.autocast is used.  
**Root Cause**: Line 263 uses `torch.cuda.amp.autocast` and line 487 uses device_type="cuda" without checking availability.  
**Fix Snippet**:
```python
# Line 263:
device_type = "cuda" if torch.cuda.is_available() else "cpu"
with torch.amp.autocast(device_type=device_type, dtype=torch.float16):

# Line 487:
with torch.amp.autocast(device_type="cuda" if torch.cuda.is_available() else "cpu", dtype=torch.float16):
```

## Bug #5: `assert_batch_consistent` Uses `.any(dim=0)` Incorrectly
**Symptom ➜ Root Cause ➜ Fix**  
✅ **Symptom**: Incorrect edge validation logic produces wrong boolean mask.  
**Root Cause**: Line 70 uses `(batch.edge_index < 0).any(dim=0)` which checks column-wise instead of element-wise.  
**Fix Snippet**:
```python
# Current buggy code (line 69-70):
mask = (batch.edge_index[0] >= N) | (batch.edge_index[1] >= N) | \
       (batch.edge_index < 0).any(dim=0)
       
# Fixed code:
mask = (batch.edge_index[0] >= N) | (batch.edge_index[1] >= N) | \
       (batch.edge_index < 0).any(dim=1)
# OR more clearly:
mask = (batch.edge_index >= N).any(dim=0) | (batch.edge_index < 0).any(dim=0)
```

---

## Patch Recommendations

### Patch #1: Wrap CUDA-Specific Blocks with Availability Checks
**Issue**: Multiple CUDA operations execute without checking if CUDA is available, causing runtime errors on CPU-only systems.
**Solution**: Add `if torch.cuda.is_available()` guards around CUDA-specific code blocks.

**Fix for Lines 52-57 (CUDA memory management):**
```python
if torch.cuda.is_available():
    torch.cuda.empty_cache()
    torch.cuda.ipc_collect()
    os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True,max_split_size_mb:128"
    torch.cuda.set_per_process_memory_fraction(0.80, device=0)
    scaler = torch.amp.GradScaler("cuda")
else:
    scaler = torch.amp.GradScaler("cpu")
```

**Fix for Line 263 (autocast in _score_edges):**
```python
device_type = "cuda" if torch.cuda.is_available() else "cpu"
with torch.amp.autocast(device_type=device_type, dtype=torch.float16):
    # 3) attention in the *tangent* space
    z_tan          = self.manifold.logmap0(h)
    causal_out, a  = self.causal_head(z_tan.unsqueeze(0), adj_mask)
```

**Fix for Line 487 (autocast in validate):**
```python
device_type = "cuda" if torch.cuda.is_available() else "cpu"
with torch.amp.autocast(device_type=device_type, dtype=torch.float16):
    y_pred, _, _ = model(*forward_args(batch), explain=False)
```

**Fix for Line 655 (autocast in training loop):**
```python
device_type = "cuda" if torch.cuda.is_available() else "cpu"
with torch.amp.autocast(device_type=device_type, dtype=torch.float16):
    y_pred, *_ = model(*forward_args(batch), explain=False)
```

**Fix for Lines 669-674 (scaler operations):**
```python
if torch.cuda.is_available():
    scaler.scale(loss).backward()
    scaler.unscale_(optimizer)
    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
    scaler.step(optimizer)
    scaler.update()
else:
    loss.backward()
    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
    optimizer.step()
scheduler.step()
```

### Patch #2: Replace Custom Negative Sampling with PyG Utility
**Issue**: Code uses PyG's `NegativeSampling` class which already handles relation types properly, so this is already implemented correctly.
**Current Implementation**: Lines 581 and 592 correctly use `NegativeSampling('binary', amount=1.0)`.
**Status**: ✅ No changes needed - PyG utility is already being used properly.

**Verification of current usage:**
```python
# Train loader (Line 581)
neg_sampling=NegativeSampling('binary', amount=1.0),

# Eval loader (Line 592) 
neg_sampling=NegativeSampling('binary', amount=1.0),
```

### Patch #3: Unify Scheduler Choice
**Issue**: Code has commented out `ReduceLROnPlateau` scheduler and uses `CyclicLR`, but the choice should be unified and configurable.
**Solution**: Create a unified scheduler configuration approach.

**Fix for Lines 628-629:**
```python
# Unified scheduler configuration
SCHEDULER_TYPE = "cyclic"  # or "plateau" - make this configurable

if SCHEDULER_TYPE == "cyclic":
    scheduler = torch.optim.lr_scheduler.CyclicLR(
        optimizer, base_lr=0.0001, max_lr=0.1, 
        step_size_up=2_000, mode="triangular2", cycle_momentum=False
    )
elif SCHEDULER_TYPE == "plateau":
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.7, patience=5, 
        threshold=0.00001, threshold_mode='rel', cooldown=0, min_lr=0, eps=1e-08
    )
else:
    # Default to step scheduler
    scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=10, gamma=0.5)
```

**Fix for scheduler step calls (Lines 664, 674):**
```python
# In training loop - handle different scheduler types
if SCHEDULER_TYPE == "cyclic":
    scheduler.step()  # Called every batch for CyclicLR
elif SCHEDULER_TYPE == "plateau":
    # ReduceLROnPlateau should be called with validation loss
    # Move this to after validation: scheduler.step(test_loss)
    pass  # Don't call here for plateau scheduler
else:
    scheduler.step()  # Default behavior
```

**Additional fix after validation (add after line 692):**
```python
# Handle plateau scheduler after validation
if SCHEDULER_TYPE == "plateau":
    scheduler.step(test_loss)
```

### Summary
- **Patch #1**: ✅ Critical - Adds CUDA availability checks (5 locations)
- **Patch #2**: ✅ Already implemented - PyG NegativeSampling utility is properly used
- **Patch #3**: ✅ Improvement - Unifies scheduler choice with configuration pattern

These patches are self-contained, require no new dependencies, and improve robustness across different hardware configurations.
